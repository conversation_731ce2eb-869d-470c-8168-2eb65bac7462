aiohappyeyeballs==2.4.4
aiohttp==3.11.11
aiosignal==1.3.2
altair==5.5.0
annotated-types==0.7.0
anyio==4.8.0
appnope==0.1.4
asttokens==3.0.0
attrs==25.1.0
beautifulsoup4==4.12.3
blinker==1.9.0
cachetools==5.5.1
certifi==2024.12.14
cffi==1.17.1
charset-normalizer==3.4.1
click==8.1.8
colorama==0.4.6
comm==0.2.2
contourpy==1.3.1
cryptography==44.0.0
cycler==0.12.1
debugpy==1.8.12
decorator==5.1.1
deprecation==2.1.0
entrypoints==0.4
executing==2.2.0
Faker==35.0.0
favicon==0.7.0
fonttools==4.55.6
frozenlist==1.5.0
gitdb==4.0.12
GitPython==3.1.44
gotrue==2.11.2
h11==0.14.0
h2==4.1.0
hpack==4.1.0
htbuilder==0.9.0
httpcore==1.0.7
httpx==0.28.1
hyperframe==6.1.0
idna==3.10
ipykernel==6.29.5
ipython==8.31.0
jaraco.classes==3.4.0
jaraco.context==6.0.1
jaraco.functools==4.1.0
jedi==0.19.2
Jinja2==3.1.5
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
jupyter_client==8.6.3
jupyter_core==5.7.2
keyring==25.6.0
keyrings.alt==5.0.2
kiwisolver==1.4.8
lxml==5.3.0
Markdown==3.7
markdown-it-py==3.0.0
markdownlit==0.0.7
MarkupSafe==3.0.2
matplotlib==3.10.0
matplotlib-inline==0.1.7
mdurl==0.1.2
more-itertools==10.6.0
multidict==6.1.0
narwhals==1.23.0
nest-asyncio==1.6.0
numpy==2.2.2
packaging==24.2
pandas==2.2.3
parso==0.8.4
pdfminer.six==20231228
pdfplumber==0.11.5
pexpect==4.9.0
pillow==11.1.0
platformdirs==4.3.6
plotly==5.24.1
postgrest==0.19.3
prometheus_client==0.21.1
prompt_toolkit==3.0.50
propcache==0.2.1
protobuf==5.29.3
psutil==6.1.1
psycopg2-binary==2.9.10
ptyprocess==0.7.0
pure_eval==0.2.3
pyarrow==19.0.0
pycparser==2.22
pydantic==2.10.6
pydantic_core==2.27.2
pydeck==0.9.1
Pygments==2.19.1
pyicloud==1.0.0
pymdown-extensions==10.14.1
pyparsing==3.2.1
PyPDF2==3.0.1
pypdfium2==4.30.1
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
pytz==2024.2
PyYAML==6.0.2
pyzmq==26.2.0
realtime==2.2.0
referencing==0.36.2
requests==2.32.3
rich==13.9.4
rpds-py==0.22.3
seqlog==0.4.0
six==1.17.0
smmap==5.0.2
sniffio==1.3.1
soupsieve==2.6
st-annotated-text==4.0.2
st-theme==1.2.3
stack-data==0.6.3
storage3==0.11.1
streamlit==1.41.1
streamlit-camera-input-live==0.2.0
streamlit-card==1.0.2
streamlit-embedcode==0.1.2
streamlit-extras==0.5.0
streamlit-faker==0.0.3
streamlit-image-coordinates==0.1.9
streamlit-keyup==0.3.0
streamlit-toggle-switch==1.0.2
streamlit-vertical-slider==2.5.5
StrEnum==0.4.15
supabase==2.12.0
supafunc==0.9.2
tenacity==9.0.0
toml==0.10.2
tornado==6.4.2
traitlets==5.14.3
typing_extensions==4.12.2
tzdata==2025.1
tzlocal==5.3.1
urllib3==2.3.0
validators==0.34.0
watchdog==6.0.0
wcwidth==0.2.13
websockets==13.1
xmltodict==0.14.2
yarl==1.18.3
uvicorn==0.34.2
