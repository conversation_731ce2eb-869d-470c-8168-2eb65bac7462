from sqlmodel import SQLModel, create_engine, Session, text
from typing import Generator
from comum.services.config import DATABASE_URL, DATABASE_URL_LOCAL
from comum.services.logger import setup_logger
logger = setup_logger()


engine = create_engine(DATABASE_URL, echo=False)
# engine = create_engine(str(postgres_config.sqlite_string.get_secret_value()), echo=False)

def fn_data_alteracao():
    sql = """
            CREATE OR REPLACE FUNCTION data_alteracao()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.data_alteracao := CURRENT_TIMESTAMP;
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
        """
    with engine.begin() as conn:
        conn.execute(text(sql))


def tr_data_alteracao(table_name: str):
    trigger_sql = f"""
                    DROP TRIGGER IF EXISTS data_alteracao ON {table_name};
                    CREATE TRIGGER data_alteracao
                    BEFORE UPDATE ON {table_name}
                    FOR EACH ROW EXECUTE FUNCTION data_alteracao();
                """
    with engine.begin() as conn:
        conn.execute(text(trigger_sql))



def fn_regista_eventos():
    sql = """
                CREATE OR REPLACE FUNCTION regista_eventos()
                RETURNS TRIGGER AS $$
                DECLARE
                    json_old jsonb;
                    json_new jsonb;
                BEGIN
                    IF TG_OP = 'INSERT' THEN
                        INSERT INTO eventos (tipo, tabela, fkid, fkid_chave)
                        VALUES (TG_OP, TG_TABLE_NAME, NEW.id, NEW.id_chave);
                        RETURN NEW;

                    ELSIF TG_OP = 'UPDATE' THEN
                        -- Remove o campo 'data_alteracao' das comparações
                        json_old := to_jsonb(OLD) - 'data_alteracao';
                        json_new := to_jsonb(NEW) - 'data_alteracao';

                        IF json_old = json_new THEN
                            -- Só mudou data_alteracao, ignora
                            RETURN NEW;
                        END IF;

                        INSERT INTO eventos (tipo, tabela, fkid, fkid_chave)
                        VALUES (TG_OP, TG_TABLE_NAME, NEW.id, NEW.id_chave);
                        RETURN NEW;

                    ELSIF TG_OP = 'DELETE' THEN
                        INSERT INTO eventos (tipo, tabela, fkid, fkid_chave)
                        VALUES (TG_OP, TG_TABLE_NAME, OLD.id, OLD.id_chave);
                        RETURN OLD;
                    END IF;

                    RETURN NULL;
                END;
                $$ LANGUAGE plpgsql;
            """
    with engine.begin() as conn:
        conn.execute(text(sql))




def tr_regista_eventos(table_name: str):
    trigger_sql = f"""
                    DROP TRIGGER IF EXISTS regista_eventos ON {table_name};
                    CREATE TRIGGER regista_eventos
                    AFTER INSERT OR UPDATE OR DELETE ON {table_name}
                    FOR EACH ROW EXECUTE FUNCTION regista_eventos();
                """
    with engine.begin() as conn:
        conn.execute(text(trigger_sql))

def database_init():
    try:
        from comum.models.estados import Estado
        from comum.models.eventos import Evento
        from comum.models.segmentos import Segmento
        from comum.models.entidades import Entidade
        from comum.models.contas import Conta
        from comum.models.ficheiros import Ficheiro
        from comum.models.recibos import Recibo
        from comum.models.recibos_codigos import ReciboCodigo
        from comum.models.contactos import Contacto
        from comum.models.contactos_telefones import ContactoTelefone
        from comum.models.faturas_cabec import FaturaCabec
        from comum.models.faturas_linhas import FaturaLinhas
        from comum.models.comprovativos import Comprovativo
        from comum.models.comprova_padroes import ComprovaPadrao
        SQLModel.metadata.create_all(engine)
        fn_data_alteracao()
        fn_regista_eventos()
        for table in SQLModel.metadata.tables.keys():
            if table != "eventos" and table != "logs" and table != "ficheiros":
                tr_data_alteracao(table)
                tr_regista_eventos(table)
        logger.info(msg="🟢 DATABASE ONLINE", ThreadName="PostgresWorker", Application="pgsqlapi")
    except Exception as e:
        logger.error(msg=f"{type(e).__name__}", ThreadName="PostgresWorker", Application="pgsqlapi", exc_info=e)
        raise ConnectionError(f"Erro ao conectar ao PostgreSQL: {e}")


def get_session() -> Generator[Session, None, None]:
    engine = create_engine(DATABASE_URL, echo=False)
    with Session(engine) as session:
        yield session