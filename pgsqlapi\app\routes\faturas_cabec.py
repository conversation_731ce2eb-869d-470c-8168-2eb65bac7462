from typing import List
from sqlmodel import Session
from fastapi import APIRouter, Depends
from comum.models.faturas_cabec import FaturaCabecBase, FaturaCabecCreate, FaturaCabecUpdate
from app.database.postgres import get_session
from app.workers.faturas_cabec import FaturasCabecWorker


faturas_cabec = APIRouter()

def get_worker(session: Session = Depends(get_session)) -> FaturasCabecWorker:
    return FaturasCabecWorker(session)


@faturas_cabec.get("/faturas_cabec", response_model=List[FaturaCabecBase])
async def lista_completa(worker: FaturasCabecWorker = Depends(get_worker)):
    return list(worker.lista())


@faturas_cabec.get("/faturas_cabec/{id}", response_model=FaturaCabecBase)
async def procura(id: int, worker: FaturasCabecWorker = Depends(get_worker)):
    return worker.procura(id)


@faturas_cabec.post("/faturas_cabec", response_model=FaturaCabecBase)
async def cria(novos_dados: FaturaCabec<PERSON><PERSON>, worker: FaturasCabecWorker = Depends(get_worker)):
    return worker.cria(novos_dados)


@faturas_cabec.patch("/faturas_cabec/{id}", response_model=FaturaCabecBase)
async def atualiza(id: int, dados: FaturaCabecUpdate, worker: FaturasCabecWorker = Depends(get_worker)):
    return worker.atualiza(id, dados)
    

@faturas_cabec.delete("/faturas_cabec/{id}")
async def remove(id: int, worker: FaturasCabecWorker = Depends(get_worker)):
    worker.remove(id)
    return {"message": f"ID: **{id}** deleted!"}


