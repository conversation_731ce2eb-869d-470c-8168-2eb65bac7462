import streamlit as st
import pandas as pd
import plotly.express as px
from src.services.supabase_client import connect
from src.querys.classQuery import Query
from src.segmentos.classSegmento import Segmento

supabase = connect()

def get_anos():
    anos = supabase.table('comprovativos').select('ano').execute().data
    if anos:
        anos_distintos = list(set(int(ano['ano']) for ano in anos))  
        anos_distintos.sort() 
    else:
        anos_distintos = []
    return anos_distintos

def get_segmentos():
    segmentos = supabase.table('segmentos').select('id_chave', 'segmento', 'categoria').order('segmento', desc=False).execute().data
    segmentos = [Segmento(**segmento) for segmento in segmentos]
    return segmentos



if 'utilizador' not in st.session_state:
    st.session_state['utilizador'] = None

if 'categorias' not in st.session_state:
    st.session_state['categorias'] = None

if 'segmento_id' not in st.session_state:
    st.session_state['segmento_id'] = None

if 'ano' not in st.session_state:
    st.session_state['ano'] = None

if 'comprovativos' not in st.session_state:
    st.session_state['comprovativos'] = pd.DataFrame()



def clean():
    st.session_state['utilizador'] = None
    st.session_state['categorias'] = None
    st.session_state['segmento_id'] = None
    st.session_state['ano'] = None


def main():
    anos = get_anos()
    segmentos = get_segmentos()
    categorias_list = sorted(set(segmento.categoria for segmento in segmentos))  # type: ignore

    with st.sidebar:
        st.divider()
        col1, col2 = st.columns([1, 1])
        with col1:
            update_clicked = st.button("Refresh")
        with col2:
            st.button("Clean", on_click=clean)



        st.divider()
        selected_user = st.pills("Utilizador", ['ricardo', 'neide'], selection_mode="multi")
        if selected_user:
            st.session_state['utilizador'] = selected_user
        else:
            st.session_state['utilizador'] = None



        st.divider()
        selected_ano = st.pills("Ano", anos, selection_mode="multi")
        if selected_ano:
            st.session_state['ano'] = selected_ano
        else:
            st.session_state['ano'] = None



        st.divider()     
        selected_categorias = st.pills("Categoria", categorias_list, selection_mode="multi")
        if selected_categorias:
            st.session_state['categorias'] = selected_categorias
        else:
            st.session_state['categorias'] = None





        st.divider()
        if selected_categorias:
            filtered_segments = [
                                    {"id_chave": segmento.id_chave, "segmento": segmento.segmento}
                                    for segmento in segmentos if segmento.categoria in selected_categorias
                                ]
            segmento_options = [filseg["segmento"] for filseg in filtered_segments]
            with st.sidebar:
                selected_segments = st.pills("Segmento", segmento_options, selection_mode="multi")
            selected_segment_ids = [segment["id_chave"] for segment in filtered_segments if segment["segmento"] in selected_segments]

            if selected_segment_ids:
                st.session_state['segmento_id'] = selected_segment_ids
            else:
                st.session_state['segmento_id'] = None

    

    # Lógica para exibir os resultados após o clique no botão Update
    if update_clicked:
        col1, col2 = st.columns([1, 1])
        # st.write(st.session_state.categorias)
        # st.write(st.session_state.segmento_id)
        # st.write(st.session_state.utilizador)
        # st.write(st.session_state.ano)

        # Executa a query e obtém os dados
        df = Query.executa_query(
                            id_chave=7,
                            query_params={
                                'categoria': st.session_state['categorias'],
                                'segmento_id': st.session_state['segmento_id'],
                                'utilizador': st.session_state['utilizador'],
                                'ano': st.session_state['ano']
                            }
                        )

        if not df.empty:
            df['valor'] = df['valor'].astype(float)
            df_debito = df[df['tipo_movimento'] == 'D']
            total_debito = round(df_debito['valor'].sum(), 2)
            debito_group = df_debito.groupby('mes')['valor'].sum().reset_index()


            df_credito = df[df['tipo_movimento'] == 'C']
            total_credito = round(df_credito['valor'].sum(), 2)
            credito_group = df_credito.groupby('mes')['valor'].sum().reset_index()

            with col1:
                st.metric("Total Débito", total_debito, border=True)
            with col2:
                st.metric("Total Crédito", total_credito, border=True)



            tab1, tab2 = st.tabs(["debito", "credito"])
            with tab1:
                st.subheader("Categoria (Débito)")
                fig_debito = px.pie(
                    df_debito,
                    names='categoria', 
                    values='valor',
                )
                st.plotly_chart(fig_debito)

                st.subheader("Segmento (Débito)")
                fig_debito = px.pie(
                    df_debito,
                    names='segmento', 
                    values='valor',
                )
                st.plotly_chart(fig_debito)


                fig_debito_group = px.bar(
                    debito_group,
                    x='mes',
                    y='valor',
                    title="Gastos Mensais",
                    text_auto=True,
                    color_discrete_sequence=['#581010'] 
                )
                st.plotly_chart(fig_debito_group)

                st.dataframe(df_debito)




            with tab2:
                st.subheader("Categoria (Crédito)")
                fig_credito = px.pie(
                    df_credito,
                    names='categoria', 
                    values='valor',
                )
                st.plotly_chart(fig_credito)

                st.subheader("Segmento (Crédito)")
                fig_credito = px.pie(
                    df_credito,
                    names='segmento', 
                    values='valor',
                )
                st.plotly_chart(fig_credito)


                fig_credito_group = px.bar(
                    credito_group,
                    x='mes',
                    y='valor',
                    title="Ganhos Mensais",
                    text_auto=True,
                    color_discrete_sequence=['#10581F'] 
                )
                st.plotly_chart(fig_credito_group)
        else:
            st.warning("Nenhum dado encontrado")
if __name__ == "__main__":
    main()