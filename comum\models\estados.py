from pydantic import field_validator
from sqlmodel import SQLModel, Field, Relationship
from typing import Optional
from datetime import datetime
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy import Column, text, <PERSON><PERSON><PERSON>, Integer, DateTime, String
from uuid import UUID


class Estado(SQLModel, table=True):
    __tablename__ = "estados"
    id: int = Field(primary_key=True, sa_column_kwargs={"autoincrement": True})
    id_chave: UUID = Field(sa_column=Column(PG_UUID(as_uuid=True), nullable=False, unique=True, server_default=text("gen_random_uuid()")))
    estado: int = Field(sa_column=Column(type_=Integer, unique=True, index=True))
    detalhe: str = Field(sa_column=Column(type_=String, nullable=True))
    cor: str = Field(sa_column=Column(type_=String, nullable=True))
    ativo: bool = Field(sa_column=Column(<PERSON>ole<PERSON>, nullable=False, server_default=text("true")))
    data_criacao: datetime = Field(sa_column=Column(type_=DateTime, nullable=False, server_default=text("now()")))
    data_alteracao: datetime = Field(sa_column=Column(type_=DateTime, nullable=False, server_default=text("now()")))


class EstadoCreate(SQLModel):
    id: Optional[int] = Field(default=None)
    estado: int
    detalhe: str
    cor: Optional[str] = Field(default=None)

    @field_validator("detalhe", mode="before")
    def upper(cls, valor: Optional[str]):
        if valor is not None:
            return valor.upper()
        return valor

    @field_validator("cor", mode="before")
    def lower(cls, valor: Optional[str]):
        if valor is not None:
            return valor.lower()
        return valor

    class Config:
        json_schema_extra = {
            "example": {
                "id": 0,
                "estado": 999,
                "detalhe": "teste_detalhe",
                "cor": "teste_cor",
                "ativo": True
            }
        }

class EstadoUpdate(SQLModel):
    estado: Optional[int] = Field(default=None)
    detalhe: Optional[str] = Field(default=None)
    cor: Optional[str] = Field(default=None)
    ativo: Optional[bool] = Field(default=None)


class EstadoBase(SQLModel):
    id: int = Field(default=None)
    id_chave: UUID = Field(default=None)
    estado: int = Field(default=None)
    detalhe: Optional[str] = Field(default=None)
    cor: Optional[str] = Field(default=None)
    ativo: bool = Field(default=None)
    data_criacao: datetime = Field(default=None)
    data_alteracao: datetime = Field(default=None)
