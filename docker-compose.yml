x-env: &default-env
  TZ: Europe/Lisbon
  PYTHONUNBUFFERED: 1
  DATABASE_URL: "*************************************************/postgres"
  PGSQLAPI_URL: http://************:5000/api/v1
  PAPERLESS_API_PORT: 5001
  PAPERLESS_API_URL: http://************:5001/api/
  PAPERLESS_API_KEY: ae0f46579cf9af6dbf858979bd044fd0029401bc
  SUPABASE_URL:  "https://hznwjwnyrtwcinugcqfr.supabase.co"
  SUPABASE_KEY: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh6bndqd255cnR3Y2ludWdjcWZyIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcxODcxNDUyMCwiZXhwIjoyMDM0MjkwNTIwfQ.-8NbzagLkihE_KRAXa5TvJ6FxaJAOT6yiIiW-BvDplM"
  SUPABASE_HOST: "aws-0-us-east-1.pooler.supabase.com"
  SUPABASE_DATABASE: "postgres"
  SUPABASE_PORT: 6543
  SUPABASE_USER: "postgres.hznwjwnyrtwcinugcqfr"
  SUPABASE_PASSWORD: "lCC8zqxxxhuSBfEY"
  DB_STRING: "dbname=postgres user=postgres.hznwjwnyrtwcinugcqfr password=lCC8zqxxxhuSBfEY host=aws-0-us-east-1.pooler.supabase.com port=6543"
  ICLOUD_USER: "<EMAIL>"
  ICLOUD_PASS: "@SKXmqn76@"

services:
  api:
    container_name: pgsqlapi
    build:
      context: .
      dockerfile: pgsqlapi/Dockerfile
    ports:
      - "5000:5000"
    volumes:
      - ./pgsqlapi:/app
      - ./comum:/app/comum
    environment:
      PYTHONPATH: /app:/comum
      <<: *default-env
    working_dir: /app
    restart: unless-stopped
    command: sh -c "uvicorn app.main:app --host 0.0.0.0 --port 5000 --reload"

# porta:5001 - PAPERLESS-NGX

  ops:
    depends_on:
      - api 
    container_name: pgsqlops
    build:
      context: .
      dockerfile: pgsqlops/Dockerfile
    ports:
      - "5002:5002"
    volumes:
      - ./pgsqlops:/app
      - ./comum:/app/comum
    environment:
      PYTHONPATH: /app:/comum
      <<: *default-env
    working_dir: /app
    restart: unless-stopped
    command: python -u app/main.py
    