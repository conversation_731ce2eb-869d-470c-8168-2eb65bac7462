import re
from datetime import datetime
from src.services.log import get_funcao
from src.services.file_service import FileService
from src.comprovativos.classComprovativo import Comprovativo
from src.services.log import LogApp

tabela = 'comprovativos'

class ComprovativoExtractorNeide:
    def __init__(self, pdf):
        self.file = FileService(file=pdf)
        self.texto = self.file.extrair_texto_pdfplumber()

    def extrair_conta(self) -> str:
        funcao = get_funcao()
        padrao = r"CONTA Nº (\d+\.\d+) PERÍODO DE (\d{4}-\d{2}-\d{2}) A (\d{4}-\d{2}-\d{2})"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1).split(sep='.')
            value = value[1]
            message = {
                'valor': value,
                'match': match
            }
            return value
        else:
            value = '00000000000000000000000'
            message = {
                'valor': value,
                'match': 'padrao nao encontrado'
            }
            return value
        

    def extrair_data(self) -> datetime:
        funcao = get_funcao()
        padrao = r"DATA DE EMISSÃO\:\s*(\d{4}-\d{2}-\d{2})"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            value = datetime.strptime(value, '%Y-%m-%d')
            message = {
                'valor': value,
                'match': match
            }
            return value
        else:
            value = datetime(1900, 1, 1, 0, 0, 0)
            message = {
                'valor': value,
                'match': 'padrao nao encontrado'
            }
            return value




    def run_extraction(self):
        funcao = get_funcao()
        dados = []
        conta = self.extrair_conta()
        data = self.extrair_data()
        ano = data.year
        mes = data.month
        linhas = self.texto.split('\n')
        linhas_filtradas = [linha for linha in linhas if re.match(r'\d{2}-\d{2} \d{2}-\d{2} \w+', linha)]
        padrao = r"(\d{2}-\d{2})\s+(\d{2}-\d{2})\s+([\w\s\*\&\-\.\€\:\/]+)\s+([-+]?\d+(?:[,]\d{2}))(?:\s+([-+]?\d+(?:[,]\d{2})))*"
        for linha in linhas_filtradas:
            match = re.search(padrao, linha)
            message = {
                'match': match
            }
            if match:
                data_valor = match.group(1)
                data_valor = data_valor + '-' + str(ano)
                data_valor = datetime.strptime(data_valor, '%d-%m-%Y')
                data_movimento = match.group(2)
                data_movimento = data_movimento + '-' + str(ano)
                data_movimento = datetime.strptime(data_movimento, '%d-%m-%Y')
                movimento = match.group(3)
                valor = match.group(4)
                valor = valor = float(valor.replace(',', '.'))
                if valor < 0:
                    tipo_movimento = 'D'
                else:
                    tipo_movimento = 'C'
                valor = abs(valor)

                
                dados.append(Comprovativo(
                    data_valor=data_valor,
                    data_movimento=data_movimento,
                    movimento=movimento,
                    valor=valor,
                    tipo_movimento=tipo_movimento,
                    ano=ano,
                    mes=mes,
                    conta=conta,
                    segmento_id=0,
                    irs=0,
                    seg_social=0
                ))                
        message = {
            'comprovativo': dados.dict(),
        }
        LogApp.log_info(classe=tabela, evento=funcao, resultado=message)
        return dados