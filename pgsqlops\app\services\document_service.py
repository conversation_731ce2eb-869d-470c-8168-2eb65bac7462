from comum.extractors.comprovativos import ComprovativoExtractor
from comum.extractors.recibos import ReciboNeideExtractor, ReciboRicardoExtractor
from comum.extractors.faturas import FaturaLidlExtractor, FaturaBox111Extractor
from comum.services.logger import setup_logger
from comum.models.paperless import PaperlessDocument
from .api_service import ApiService
from datetime import date


logger = setup_logger()

class DocumentService:
    def __init__(self, api_service: ApiService):
        self.api_service = api_service
        self.processed_field_id = 1  # ID do campo "processado"
        self.tag_db_id = 10  # ID da tag para dados extraídos para DB
        self.ricardo_id = 1
        self.neide_id = 2

    def process_document(self, documento: PaperlessDocument) -> None:
        try:
            if documento.document_type == 1:
                self._process_comprovativo(documento)
            elif documento.document_type == 2:
                self._process_recibo(documento)
            elif documento.document_type == 3:
                self._process_fatura(documento)
            
            else:
                pass
        except Exception as e:
            logger.error(msg=f"Erro ao processar documento {documento.id}: {type(e).__name__} - {str(e)}",ThreadName="DocumentService",Application="pgsqlops", funcao="process_document", exc_info=str(e)) # type: ignore

################################################################################################# COMPROVATIVOS #################################################################################################

    def _process_comprovativo(self, documento: PaperlessDocument) -> None:
        try:
            if documento.correspondent == self.ricardo_id:
                # Extrair dados do comprovativo de RICARDO
                comprovativo = ComprovativoExtractor(documento.content, documento.original_file_name).run_extraction()


                # Criar comprovativo no PGSQL
                comprovativo_response = self.api_service.create_comprovativo(comprovativo)
            
            if comprovativo_response:
                # Extrair id de tag do ano do documento
                ano_tag = self.api_service.get_tag_by_name(str(comprovativo_response.ano))
                if ano_tag:
                    ano_tag = ano_tag.all[0]
                else:
                    ano_tag = None

                tags = documento.tags
                if self.tag_db_id not in tags:
                    tags.append(self.tag_db_id)
                if ano_tag and ano_tag not in tags:
                    tags.append(ano_tag)
                

                data_criacao = comprovativo_response.data_valor.strftime("%Y-%m-%d")

                payload = {
                                "title": str(comprovativo_response.id_chave),
                                "tags": tags,
                                "custom_fields": [
                                    {
                                        "field": self.processed_field_id,
                                        "value": True
                                    }
                                ],
                                "created_date": data_criacao
                            }
                self.api_service.update_document(documento.id, payload)
            else:
                logger.warning(msg=f"Comprovativo {documento.id} não processado.", ThreadName="DocumentService", Application="pgsqlops", funcao="_process_comprovativo")
        except Exception as e:
            logger.error(msg=f"Erro ao processar comprovativo {documento.id}: {type(e).__name__} - {str(e)}",ThreadName="DocumentService",Application="pgsqlops",funcao="_process_comprovativo",exc_info=str(e)) # type: ignore

################################################################################################# RECIBOS #################################################################################################
    
    def _process_recibo(self, documento: PaperlessDocument) -> None:
        try:
            tags = documento.tags
            
            if documento.correspondent == self.neide_id:
                recibo = ReciboNeideExtractor(documento.content, documento.original_file_name).run_extraction()
            elif documento.correspondent == self.ricardo_id:
                recibo = ReciboRicardoExtractor(documento.content, documento.original_file_name).run_extraction()
            else:
                logger.warning(msg=f"Correspondente não reconhecido para o recibo: {documento.id}", ThreadName="DocumentService", Application="pgsqlops", funcao="_process_recibo")
            
            # Criar recibo no PGSQL
            recibo_response = self.api_service.create_recibo(recibo)
            if recibo_response:
                ano_tag = self.api_service.get_tag_by_name(str(recibo_response.ano))
                if ano_tag:
                    ano_tag = ano_tag.all[0]
                else:
                    ano_tag = None


                if self.tag_db_id not in tags:
                    tags.append(self.tag_db_id)
                if ano_tag and ano_tag not in tags:
                    tags.append(ano_tag)
                payload = {
                                "title": str(recibo_response.id_chave),
                                "tags": tags,
                                "custom_fields": [
                                    {
                                        "field": self.processed_field_id,
                                        "value": True
                                    }
                                ],
                                "created_date": recibo_response.data_fecho.strftime("%Y-%m-%d")
                            }
                
                self.api_service.update_document(documento.id, payload)
                
        except Exception as e:
            logger.error(msg=f"Erro ao processar recibo {documento.id}: {type(e).__name__} - {str(e)}",ThreadName="DocumentService",Application="pgsqlops",exc_info=str(e)) # type: ignore
    
################################################################################################# FATURAS #################################################################################################

    def _process_fatura(self, documento: PaperlessDocument) -> None:
        try:
            tags = documento.tags
            cabec = None
            linhas = None
            
            if 11 in tags:
                cabec, linhas = FaturaLidlExtractor(documento.content, documento.original_file_name).run_extraction()
            elif 12 in tags:
                cabec, linhas = FaturaBox111Extractor(documento.content, documento.original_file_name).run_extraction()
            else:
                logger.info(msg=f"Tags:{tags} sem avaliacao de fatura: {documento.id}", ThreadName="DocumentService", Application="pgsqlops", funcao="_process_fatura")
            
            # Criar fatura no PGSQL
            if cabec and linhas:
                cabec_response, linhas_response = self.api_service.create_fatura(cabec, linhas)
                if cabec_response and linhas_response:
                    ano_tag = self.api_service.get_tag_by_name(str(cabec_response.ano))
                    if ano_tag:
                        ano_tag = ano_tag.all[0]
                    else:
                        ano_tag = None

                # ATUALIZAR DOCUMENTO NGX
                if self.tag_db_id not in tags:
                    tags.append(self.tag_db_id)
                if ano_tag and ano_tag not in tags:
                    tags.append(ano_tag)
                payload = {
                                "title": str(cabec_response.id_chave),
                                "tags": tags,
                                "custom_fields": [
                                    {
                                        "field": self.processed_field_id,
                                        "value": True
                                    }
                                ],
                                "created_date": cabec_response.data.strftime("%Y-%m-%d")
                            }
                
                self.api_service.update_document(documento.id, payload)
                
        except Exception as e:
            logger.error(msg=f"Erro ao processar fatura {documento.id}: {type(e).__name__} - {str(e)}",ThreadName="DocumentService",Application="pgsqlops",exc_info=str(e)) # type: ignore
    
    
    