import zipfile
import io
import streamlit as st
from src.faturas.workerFatura import process_uploaded_file, create_zip
from src.services.supabase_client import connect
from src.logs.classLogsApp import LogApp    


supabase = connect()
bucket = 'documentos/faturas/lidl'


def set_downloaded():
    st.session_state["downloaded"] = True

def initialize_session_state():
    if "analisar" not in st.session_state:
        st.session_state["analisar"] = False
    if "upload" not in st.session_state:
        st.session_state["upload"] = False
    if "downloaded" not in st.session_state:
        st.session_state["downloaded"] = False
    if "entidade" not in st.session_state:
        st.session_state["entidade"] = None



def analisador(uploaded_files, entidade):
    progress_bar = st.sidebar.progress(0)
    status_text = st.sidebar.empty()
    total_files = len(uploaded_files)

    for index, file in enumerate(uploaded_files, start=1):
        with st.expander(f"Arquivo ({index}): {file.name}"):
            try:
                extract_result = process_uploaded_file(file=file, entidade=entidade, save=False)
                st.json(extract_result.cabecalho)
            except Exception as e:
                st.error(f"Erro ao processar o arquivo {file.name}: {e}")

        progress = int((index / total_files) * 100)
        progress_bar.progress(progress)
        status_text.text(f"Analisado: {index}/{total_files}")




def uploader(uploaded_files, entidade):
    st.session_state["downloaded"] = False    
    progress_bar = st.sidebar.progress(0)
    status_text = st.sidebar.empty()
    total_files = len(uploaded_files)
    files_toget = []


    for index, pdf in enumerate(uploaded_files, start=1):
        with st.expander(f"Arquivo: {pdf.name}"):
            try:
                insert_result = process_uploaded_file(file=pdf, entidade=entidade, save=True)
                novo_nome = insert_result.data[0]['id_chave']
                novo_nome = f"{novo_nome}.pdf"
                for result in insert_result:
                    st.json(result) 
                files_toget.append((novo_nome, pdf.getvalue()))
                progress = int((index / total_files) * 100)
                progress_bar.progress(progress)
                status_text.text(f"Enviado: {index}/{total_files}")
            except Exception as e:
                st.error(f"upload error on {pdf.name}: {e}")
                status_text.text(f"Erro")

    if files_toget:
        zip_data = create_zip(files_toget)
        st.sidebar.download_button(
                                    label="download",
                                    data=zip_data,
                                    file_name="recibos.zip",
                                    mime="application/zip",
                                    on_click=set_downloaded,
                                )


def main():
    initialize_session_state()


    st.title("Upload de Faturas")
    entidade = st.session_state["entidade"]
    uploaded_files = st.file_uploader("Selecione as faturas", type=["pdf"], accept_multiple_files=True)
    if uploaded_files:
        st.session_state.downloaded = False
        st.session_state.analisar = False
        st.session_state.upload = False
        st.sidebar.write(f'{len(uploaded_files)} arquivo(s)')
        total_files = len(uploaded_files)
        col1, col2 = st.columns(2)
        with st.sidebar:
            with col1:
                if st.sidebar.button(f"analisar"):
                    st.session_state["analisar"] = True
                    st.session_state["upload"] = False

            with col2:
                if st.sidebar.button(f"upload"):
                    st.session_state["upload"] = True
                    st.session_state["analisar"] = False

        if st.session_state["analisar"]:
            analisador(uploaded_files, entidade)

        if st.session_state["upload"]:
            uploader(uploaded_files, entidade)


        if st.session_state["downloaded"]:
            LogApp.log_warning(
                resultado={"file_name": "faturas.zip", "num_files": total_files},
                classe="faturas",
                evento="download"
            )

if __name__ == "__main__":
    main()