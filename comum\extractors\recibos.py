import re
from typing import Optional
from datetime import datetime, date
from comum.models.recibos import ReciboCreate
from comum.services.logger import setup_logger
logger = setup_logger()

class ReciboRicardoExtractor:
    def __init__(self, texto, nome_ficheiro):
        self.texto = texto
        self.nome_ficheiro = nome_ficheiro
        self.recibo = ReciboCreate()

    def extrair_data_fecho(self) -> date:
        padrao = r'Data Fecho (\d{2}[-/]\d{2}[-/]\d{4})'
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            value = value.replace('-', '/')
            value = datetime.strptime(value, '%d/%m/%Y').date()
        else:
            value = date(1900, 1, 1)
        return value


    def extrair_nome(self):
        padrao = r'Nome\s+(.*?)\s+Período'
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
        else:
            value = None
        return value
    
    def extrair_id_beneficiario(self):
        padrao = r'N.º Benef.\s+(\d+)'
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
        else:
            value = None
        return value

    def extrair_dias_trabalhados(self):
        padrao = r'N.\s+Dias\s+Mês:\s+([\d,.]+)'
        match = re.search(padrao, self.texto)
        if match:
            value = int(float(match.group(1)))
        else:
            value = None
        return value
    
    def extrair_nif(self):
        padrao = r'N.º Contrib.\s+(\d+)'
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
        else:
            value = None
        return value
    
    def extrair_seguro(self):
        padrao = r'Seguro\s+(.+)'
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
        else:
            value = None
        return value

    
    def extrair_id_colaborador(self):
        padrao = r'N.º Mecan.\D*(\d+)'
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
        else:
            value = None
        return value

    def extrair_valor_liquido(self):
        # padrao = r'(\d{1,3}(?:\.\d{3})*,\d{2})\s*Total Pago \( EUR \)'
        padrao = r'(\d{1,3}(?:[ .]\d{3})*,\d{2})\s*Total Pago \( EUR \)'
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            value = float(value.replace(' ', '').replace('.', '').replace(',', '.'))
        else:
            value = 0.0
        return value

    def extrair_valor_bruto(self):
        padrao = r'Total\s+([\d\s.,]+)\s+[\d\s.,]+\n'
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            value = value.replace(' ', '')
            value = value.replace('.', '')
            value = value.replace(',', '.')
            value = float(value)
        else:
            value = 0.0
        return value

    def extrair_descontos_totais(self):
        padrao = r'Total\s+([\d\s,.]+)\s+[\d\s,.]+\n'
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            value = float(value.replace(' ', '').replace('.', '').replace(',', '.'))
            value = round(value, 2)
        else:
            value = 0.0
        return value

    def extrair_categoria(self):
        padrao = r'Categoria Vencimento\s+(\S+)\s+(.*)'
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(2)
            value = re.sub(r'[^a-zA-ZçáéíóúãõâêôÇÁÉÍÓÚÃÕÂÊÔ\s]', '', value).strip()
        else:
            value = None
        return value
    
    def extrair_departamento(self):
        padrao = r'Departamento\s+(.+)'
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            partes = value.split('Departamento')
            partes = [p.strip() for p in partes if p.strip()]
            value = partes[0] if partes else value
        else:
            value = None
        return value

    def extrair_vencimento_hora(self):
        padrao = r'Venc. / Hora\s+([\d,.]+)'
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            value = value.replace(' ', '')
            value = value.replace('.', '')
            value = value.replace(',', '.')
            value = float(value)
        else:
            value = 0.0
        return value

    def extrair_vencimento_base(self):
        padrao = r'Categoria Vencimento\s+([\d\s,.]+)\s+'
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            value = float(value.replace(' ', '').replace('.', '').replace(',', '.'))
        else:
            value = 0.0
        return value


    def run_extraction(self) -> ReciboCreate:
        try:
            self.recibo.data_fecho = self.extrair_data_fecho()
            self.recibo.ano = self.recibo.data_fecho.year
            self.recibo.mes = self.recibo.data_fecho.month
            self.recibo.categoria = self.extrair_categoria()
            self.recibo.departamento = self.extrair_departamento()
            self.recibo.vencimento_hora = self.extrair_vencimento_hora()
            self.recibo.id_beneficiario = self.extrair_id_beneficiario()
            self.recibo.id_colaborador = self.extrair_id_colaborador()
            self.recibo.seguro = self.extrair_seguro()
            self.recibo.dias_trabalhados = self.extrair_dias_trabalhados()
            self.recibo.vencimento_base = self.extrair_vencimento_base()
            self.recibo.valor_bruto = self.extrair_valor_bruto()
            self.recibo.valor_liquido = self.extrair_valor_liquido()
            self.recibo.descontos_totais = self.extrair_descontos_totais()
            self.recibo.nome = self.extrair_nome()
            self.recibo.nif = self.extrair_nif()
            if self.recibo.valor_liquido is not None and self.recibo.dias_trabalhados not in (None, 0):
                self.recibo.vencimento_hora = round(self.recibo.valor_liquido / self.recibo.dias_trabalhados / 8, 2)
            else:
                self.recibo.vencimento_hora = 0.0
            logger.info(msg=f"RECIBO EXTRACTION: {self.nome_ficheiro}", ThreadName="ReciboRicardoExtractor", Application="pgsqlapi", dados=self.recibo)
            return self.recibo
        except Exception as e:
            mensagem = str(e).strip().split("\n")[0]
            logger.error(msg=f"{type(e).__name__} - {mensagem}", ThreadName="ReciboRicardoExtractor", Application="pgsqlapi", exc_info=e)
            raise ValueError(f"ERRO AO EXTRAIR RECIBO: {mensagem}")






class ReciboNeideExtractor:
    def __init__(self, texto, nome_ficheiro):
        self.texto = texto
        self.nome_ficheiro = nome_ficheiro
        self.recibo = ReciboCreate()

    def extrair_data_fecho(self) -> Optional[date]:
        padrao = r"\b\d{2}-\d{2}-\d{4}\b"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(0)
            value = value.replace('-', '/')
            value = datetime.strptime(value, '%d/%m/%Y')
            return value.date()
        else:
            value = date(1900, 1, 1)
            return value
        


    def extrair_vencimento_base(self) -> Optional[float]:
        padrao = r"Salário Base:\s*€\s*([\d\.,]+)"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            value = value.replace('.', '').replace(',', '.')
            value = float(value)
            return value
        else:
           
            return 0.0
        


    def extrair_valor_bruto(self) -> Optional[float]:
        padrao = r"Total Ilíquido:\s*€\s*([\d\.,]+)"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            value = value.replace('.', '').replace(',', '.')
            value = float(value)
            return value
        else:
           
            return 0.0



    def extrair_valor_liquido(self) -> Optional[float]:
        padrao = r"Líquido a receber:\s*€\s*([\d\.,]+)"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            value = value.replace('.', '').replace(',', '.')
            value = float(value)
            return value
        else:
           
            return 0.0
        


    def extrair_descontos_totais(self) -> Optional[float]:
        padrao = r"Total de Descontos:\s*€\s*([\d\.,]+)"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            value = value.replace('.', '').replace(',', '.')
            value = float(value)
            return value
        else:
            value = 0
            return value
        


    def extrair_nome(self) -> Optional[str]:
        padrao = r"\d{5,}\s+([A-Za-zÀ-ÿ\s]+)\n"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            return value
        else:
            return None



    def extrair_nif(self) -> Optional[str]:
        padrao = r"N\.I\.F\. ?: ?(\d+)"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            return value
        else:
            return None
        
    def extrair_dias_trabalhados(self) -> Optional[int]:
        padrao = r"^A61 S\.Alimentaçao Cartão (\d+),\d{2}"
        match = re.search(padrao, self.texto, re.MULTILINE)
        if match:
            value = match.group(1)
            value = value.replace('.', '').replace(',', '.')
            value = round(float(value), 0)
            value = int(value)
            return value
        else:
            value = 9999
            return value

    def extrair_id_beneficiario(self) -> Optional[int]:
        return None

    def extrair_id_colaborador(self) -> Optional[int]:
        return None

    def extrair_seguro(self) -> Optional[str]:
        return None

    def extrair_categoria(self) -> Optional[str]:
        return None

    def extrair_departamento(self) -> Optional[str]:
        return None


    def run_extraction(self):
        try:
            self.recibo.data_fecho = self.extrair_data_fecho()
            if self.recibo.data_fecho is not None:
                self.recibo.ano = self.recibo.data_fecho.year
                self.recibo.mes = self.recibo.data_fecho.month
            else:
                self.recibo.ano = None
                self.recibo.mes = None
            self.recibo.vencimento_base = self.extrair_vencimento_base()
            self.recibo.valor_bruto = self.extrair_valor_bruto()
            self.recibo.valor_liquido = self.extrair_valor_liquido()
            self.recibo.descontos_totais = self.extrair_descontos_totais()
            self.recibo.dias_trabalhados = self.extrair_dias_trabalhados()
            self.recibo.nome = self.extrair_nome()
            self.recibo.nif = self.extrair_nif()
            self.recibo.id_beneficiario = self.extrair_id_beneficiario()
            self.recibo.id_colaborador = self.extrair_id_colaborador()
            self.recibo.seguro = self.extrair_seguro()
            self.recibo.categoria = self.extrair_categoria()
            self.recibo.departamento = self.extrair_departamento()
            if self.recibo.valor_liquido is not None and self.recibo.dias_trabalhados not in (None, 0):
                self.recibo.vencimento_hora = round(self.recibo.valor_liquido / self.recibo.dias_trabalhados / 8, 2)
            else:
                self.recibo.vencimento_hora = 0.0
            logger.info(msg=f"RECIBO EXTRACTION: {self.nome_ficheiro}", ThreadName="ReciboNeideExtractor", Application="pgsqlapi", _resultado=self.recibo.model_dump(mode="json", exclude_none=True))
            return self.recibo
        except Exception as e:
            mensagem = str(e).strip().split("\n")[0]
            logger.error(msg=f"{type(e).__name__} - {mensagem}", ThreadName="ReciboNeideExtractor", Application="pgsqlapi", exc_info=e)
            raise ValueError(f"ERRO AO EXTRAIR RECIBO: {mensagem}")