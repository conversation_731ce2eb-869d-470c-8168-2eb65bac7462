import streamlit as st
from streamlit_extras.switch_page_button import switch_page 
# Opções
modos = ["-", "upload_supabase", "analise"]
entidades = ["-", "lidl", "microsoft", "via_verde"]

# Configuração da página
st.set_page_config(layout="wide")

def carregar_entidade(entidade):
    if entidade == "lidl":
        import pages.faturas.lidl_supload as fl
        fl.main()
    elif entidade == "microsoft":
        import pages.faturas.microsoft_upload as fm
        fm.main()
    elif entidade == "via_verde":
        import pages.faturas.via_verde_upload as fv
        fv.main()

def analisar_faturas():
    import pages.faturas.faturas_analise as fa
    fa.main()

def main():
    entidade = None

    # Barra lateral para seleção de modos e entidades
    with st.sidebar:
        modo = st.selectbox("Selecionar modo:", modos, index=0)
        if modo == "-":
            st.warning("Nenhum modo selecionado.")
        elif modo == "upload_supabase":
            entidade = st.selectbox("Selecionar entidade:", entidades, index=0)
            if entidade:
                st.session_state["entidade"] = entidade

    # Lógica principal
    if modo == "upload_supabase":
        if entidade and entidade != "-":
            carregar_entidade(entidade)
    elif modo == "analise":
        analisar_faturas()

if __name__ == "__main__":
    if not st.session_state.get('authenticated'):
        switch_page("main")
    else:
        if 'selected_user' not in st.session_state:
            st.session_state['selected_user'] = None
        main()
