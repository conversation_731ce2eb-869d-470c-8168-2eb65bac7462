from typing import List
from sqlmodel import Session
from fastapi import APIRouter, Depends
from app.database.postgres import get_session
from app.workers.comprova_padroes import ComprovaPadroesWorker
from comum.models.comprova_padroes import ComprovaPadraoBase, ComprovaPadraoCreate, ComprovaPadraoUpdate


comprova_padroes_router = APIRouter()

def get_worker(session: Session = Depends(get_session)) -> ComprovaPadroesWorker:
    return ComprovaPadroesWorker(session)


@comprova_padroes_router.get("/comprova_padroes", response_model=List[ComprovaPadraoBase])
async def lista_completa(worker: ComprovaPadroesWorker = Depends(get_worker)):
    return list(worker.lista())


@comprova_padroes_router.get("/comprova_padroes/{id}", response_model=ComprovaPadraoBase)
async def procura(id: int, worker: ComprovaPadroesWorker = Depends(get_worker)):
    return worker.procura(id)


@comprova_padroes_router.post("/comprova_padroes", response_model=ComprovaPadraoBase)
async def cria(novos_dados: ComprovaPadraoCreate, worker: ComprovaPadroesWorker = Depends(get_worker)):
    return worker.cria(novos_dados)


@comprova_padroes_router.patch("/comprova_padroes/{id}", response_model=ComprovaPadraoBase)
async def atualiza(id: int, dados: ComprovaPadraoUpdate, worker: ComprovaPadroesWorker = Depends(get_worker)):
    return worker.atualiza(id, dados)
    

@comprova_padroes_router.delete("/comprova_padroes/{id}")
async def remove(id: int, worker: ComprovaPadroesWorker = Depends(get_worker)):
    worker.remove(id)
    return {"message": f"ID: **{id}** deleted!"}