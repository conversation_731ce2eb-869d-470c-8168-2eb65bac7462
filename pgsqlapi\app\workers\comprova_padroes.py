from typing import List
from fastapi import HTTPException
from sqlmodel import Session, select
from sqlalchemy.exc import IntegrityError, OperationalError, DataError, ProgrammingError
from comum.services.logger import setup_logger
from comum.models.comprova_padroes import ComprovaPadrao, ComprovaPadraoCreate, ComprovaPadraoUpdate, ComprovaPadraoBase
logger = setup_logger()

class ComprovaPadroesWorker:
    def __init__(self, session: Session):
        self.session = session

    def gestor_erros(self, e: Exception) -> None:
        if isinstance(e, IntegrityError):
            mensagem = str(e.orig).strip().split("\n")[0]
            logger.error(msg=f"{type(e).__name__} - {mensagem}", ThreadName="ComprovaPadroesWorker", Application="pgsqlapi", exc_info=e.orig)
            raise HTTPException(status_code=400, detail=f"Erro de integridade: {mensagem}")
        elif isinstance(e, OperationalError):
            mensagem = str(e.orig).strip().split("\n")[0]
            logger.error(msg=f"{type(e).__name__} - {mensagem}", ThreadName="ComprovaPadroesWorker", Application="pgsqlapi", exc_info=e.orig)
            raise HTTPException(status_code=500, detail=f"Erro no banco de dados: {mensagem}")
        elif isinstance(e, DataError):
            mensagem = str(e.orig).strip().split("\n")[0]
            logger.error(msg=f"{type(e).__name__} - {mensagem}", ThreadName="ComprovaPadroesWorker", Application="pgsqlapi", exc_info=e.orig)
            raise HTTPException(status_code=400, detail=f"Erro de dados inválidos: {mensagem}")
        elif isinstance(e, ProgrammingError):
            mensagem = str(e.orig).strip().split("\n")[0]
            logger.error(msg=f"{type(e).__name__} - {mensagem}", ThreadName="ComprovaPadroesWorker", Application="pgsqlapi", exc_info=e.orig)
            raise HTTPException(status_code=400, detail=f"Erro de programação SQL: {mensagem}")
        else:
            mensagem = str(e).strip().split("\n")[0]
            logger.error(msg=f"{type(e).__name__} - {mensagem}", ThreadName="ComprovaPadroesWorker", Application="pgsqlapi", exc_info=e)
            raise HTTPException(status_code=400, detail=f"Erro inesperado: {mensagem}")


    def lista(self) -> List[ComprovaPadraoBase]:
        try:
            query = select(ComprovaPadrao)
            return list(self.session.exec(query).all())
        except Exception as e:
            self.gestor_erros(e)
            raise  

    def procura(self, id: int) -> ComprovaPadraoBase:
        try:
            resultado = self.session.get(ComprovaPadrao, id)
            if not resultado:
                raise HTTPException(status_code=404, detail=f"ID: {id} não encontrado")
            return resultado
        except Exception as e:
            self.gestor_erros(e)
            raise


    def cria(self, dados: ComprovaPadraoCreate) -> ComprovaPadrao:
        try:
            novo = ComprovaPadrao(**dados.model_dump(exclude_unset=True, exclude_none=True))
            self.session.add(novo)
            self.session.commit()
            self.session.refresh(novo)
            logger.warning(msg=f"COMPROVA PADRAO INSERT: {novo.id_chave}", ThreadName="ComprovaPadroesWorker", Application="pgsqlapi", dados=novo.model_dump())
            return novo
        except Exception as e:
            self.gestor_erros(e)
            raise


    def atualiza(self, id: int, novos_dados: ComprovaPadraoUpdate) -> ComprovaPadrao:
        try:
            resultado = self.procura(id)
            novos_dados = novos_dados.model_dump(exclude_unset=True)
            for key, value in novos_dados.items():
                setattr(resultado, key, value)
            self.session.add(resultado)
            self.session.commit()
            self.session.refresh(resultado)
            logger.warning(msg=f"COMPROVA PADRAO UPDATE: {resultado.id_chave}", ThreadName="ComprovaPadroesWorker", Application="pgsqlapi", dados=novos_dados)
            return resultado
        except Exception as e:
            self.gestor_erros(e)
            raise


    def remove(self, id: int) -> None:
        try:
            resultado = self.procura(id)
            self.session.delete(resultado)
            self.session.commit()
            logger.warning(msg=f"COMPROVA PADRAO DELETE: {resultado.id_chave}", ThreadName="ComprovaPadroesWorker", Application="pgsqlapi", dados=resultado.model_dump())
        except Exception as e:
            self.gestor_erros(e)
            raise

