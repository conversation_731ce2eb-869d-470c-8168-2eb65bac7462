import streamlit as st
# from src.services.cron import iniciar_cron
from src.services.logger import setup_logger
logger = setup_logger()



def initialize_session_state():
    if 'authenticated' not in st.session_state:
        st.session_state['authenticated'] = True
    
    if 'user' not in st.session_state:
        st.session_state['user'] = None
    
    # if 'cron_on' not in st.session_state:
    #     st.session_state['cron_on'] = True
    #     iniciar_cron()



def main():
    st.set_page_config(page_title="webapp",layout="centered",initial_sidebar_state="expanded",page_icon="📄")
    
    initialize_session_state()
    
    # if not st.session_state['authenticated']:
    #     with st.form(key='login_form'):
    #         user = st.text_input("Email:")
    #         password = st.text_input("Password:", type="password")
    #         submit_button = st.form_submit_button(label='Login')
    #         if submit_button:
    #             login(user, password)
    
    # if st.session_state['authenticated']:
    #     st.session_state['sidebar_state'] = "expanded"
    #     st.info(f"Bem vindo, {st.session_state['user']}")
    #     if st.button('logout'):
    #         st.session_state['authenticated'] = False
    #         st.session_state['user'] = None
    #         st.session_state['sidebar_state'] = "collapsed"

if __name__ == "__main__":
    main()
    