import streamlit as st
from src.services.supabase_client import connect

supabase = connect()

def login(user, password):
    """Realiza a autenticação do usuário."""
    if not user or not password:
        st.error("Preencha todos os campos.")
        return False

    try:
        response = supabase.auth.sign_in_with_password({"email": user, "password": password})
        if response.user.aud == 'authenticated':  # type: ignore
            st.session_state['authenticated'] = True
            st.session_state['user'] = response.user.email  # type: ignore
            return True
    except Exception as e:
        st.error(f"Erro ao efetuar login: {e}")
        return False
