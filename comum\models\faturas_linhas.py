from sqlmodel import SQLModel, Field, Relationship
from typing import Optional
from datetime import datetime
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy import Column, text, Boolean, DateTime, String, Float, Integer
from uuid import UUID


class FaturaLinhas(SQLModel, table=True):
    __tablename__ = "faturas_linhas"
    id: int = Field(primary_key=True, sa_column_kwargs={"autoincrement": True})
    id_chave: UUID = Field(sa_column=Column(PG_UUID(as_uuid=True), nullable=False, unique=True, server_default=text("gen_random_uuid()")))
    fatura_id: int = Field(foreign_key="faturas_cabec.id")
    fatura: str = Field(sa_column=Column(type_=String, nullable=True))
    artigo: str = Field(sa_column=Column(type_=String, nullable=True))
    base: float = Field(sa_column=Column(type_=Float, nullable=True, server_default=text("0.0")))
    iva: float = Field(sa_column=Column(type_=Float, nullable=True, server_default=text("0.0")))
    desconto: float = Field(sa_column=Column(type_=Float, nullable=True, server_default=text("0.0")))
    valor: float = Field(sa_column=Column(type_=Float, nullable=True, server_default=text("0.0")))
    valor_bruto: float = Field(sa_column=Column(type_=Float, nullable=True, server_default=text("0.0")))
    tx_iva: float = Field(sa_column=Column(type_=Float, nullable=True, server_default=text("0.0")))
    data: datetime = Field(sa_column=Column(type_=DateTime, nullable=False))
    ano: int = Field(sa_column=Column(type_=Integer, nullable=True))
    mes: int = Field(sa_column=Column(type_=Integer, nullable=True))
    ativo: bool = Field(sa_column=Column(Boolean, nullable=False, server_default=text("true")))
    data_criacao: datetime = Field(sa_column=Column(type_=DateTime, nullable=False, server_default=text("now()")))
    data_alteracao: datetime = Field(sa_column=Column(type_=DateTime, nullable=False, server_default=text("now()")))

    fatura_cabec_rel: Optional["FaturaCabec"] = Relationship(back_populates="faturas_linhas_rel") # type: ignore

class FaturaLinhasCreate(SQLModel):
    fatura_id: int = Field(default=0)
    fatura: str = Field(default=None)
    artigo: Optional[str] = Field(default=None)
    base: float = Field(default=0.0)
    iva: float = Field(default=0.0)
    desconto: float = Field(default=0.0)
    valor: float = Field(default=0.0)
    valor_bruto: float = Field(default=0.0)
    tx_iva: float = Field(default=0.0)
    data: datetime = Field(default=None)
    ano: Optional[int] = Field(default=None)
    mes: Optional[int] = Field(default=None)
    ativo: Optional[bool] = Field(default=True)

    class Config:
        json_schema_extra = {
            "example": {
                "fatura_id": 0,
                "fatura": "teste_fatura",
                "artigo": "teste_artigo",
                "base": 100.0,
                "iva": 23.0,
                "desconto": 0.0,
                "valor": 123.0,
                "valor_bruto": 123.0,
                "tx_iva": 23.0,
                "data": "2000-01-01T00:00:00",
                "ano": 2000,
                "mes": 1,
                "ativo": True
            }
        }


class FaturaLinhasUpdate(SQLModel):
    fatura_id: Optional[int] = Field(default=None)
    fatura: Optional[str] = Field(default=None)
    artigo: Optional[str] = Field(default=None)
    base: Optional[float] = Field(default=None)
    iva: Optional[float] = Field(default=None)
    desconto: Optional[float] = Field(default=None)
    valor: Optional[float] = Field(default=None)
    valor_bruto: Optional[float] = Field(default=None)
    tx_iva: Optional[float] = Field(default=None)
    data: Optional[datetime] = Field(default=None)
    ano: Optional[int] = Field(default=None)
    mes: Optional[int] = Field(default=None)
    ativo: Optional[bool] = Field(default=None)


class FaturaLinhasBase(SQLModel):
    id: int = Field(default=None)
    id_chave: UUID = Field(default=None)
    fatura_id: Optional[int] = Field(default=None)
    fatura: Optional[str] = Field(default=None)
    artigo: Optional[str] = Field(default=None)
    base: float = Field(default=0.0)
    iva: float = Field(default=0.0)
    desconto: float = Field(default=0.0)
    valor: float = Field(default=0.0)
    valor_bruto: float = Field(default=0.0)
    tx_iva: float = Field(default=0.0)
    data: Optional[datetime] = Field(default=None)
    ano: Optional[int] = Field(default=None)
    mes: Optional[int] = Field(default=None)
    ativo: bool = Field(default=None)
    data_criacao: datetime = Field(default=None)
    data_alteracao: datetime = Field(default=None)
