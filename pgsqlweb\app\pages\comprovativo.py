import streamlit as st
from streamlit_extras.switch_page_button import switch_page  # type: ignore[import]

utilizadores = ['ricardo', 'neide'] 
modos = ['upload_supabase','edit', 'padroes_edit', 'insert', 'analise']

def main():
    st.set_page_config(layout="wide")
    with st.sidebar:
        modo = st.radio("Selecionar modo:", modos, index=0)



    if modo == "upload_supabase":
        with st.sidebar:
            selected_user = st.selectbox("Selecionar utilizador", ["-"] + utilizadores)
        if selected_user != "-":
            st.session_state['selected_user'] = selected_user
            try:
                import pages.comprovativos.comprovativos_supload as up
                up.main()
            except ModuleNotFoundError as e:
                st.error(e)



    elif modo == "edit":
        with st.sidebar:
            selected_user = None
            selected_user = st.pills("Selecionar utilizador", utilizadores, selection_mode="single")
            if selected_user:
                st.session_state['selected_user'] = selected_user
        import pages.comprovativos.comprovativos_edit as ce
        ce.main()


    elif modo == "padroes_edit":
        import pages.comprovativos.comprovativos_padroes as cp
        cp.main()


    elif modo == "insert":
        import pages.comprovativos.comprovativos_inserir as ci
        ci.main()


    elif modo == "analise":
        import pages.comprovativos.comprovativos_analise as ca
        ca.main()








if __name__ == "__main__":
    if not st.session_state.get('authenticated'):
        switch_page("main")
    else:
        if 'selected_user' not in st.session_state:
            st.session_state['selected_user'] = None
        main()