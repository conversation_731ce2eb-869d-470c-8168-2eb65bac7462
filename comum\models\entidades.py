from pydantic import field_validator
from sqlmodel import SQLModel, Field, Relationship
from typing import Optional, List
from datetime import datetime
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy import Column, text, Boolean, DateTime, String
from uuid import UUID


class Entidade(SQLModel, table=True):
    __tablename__ = "entidades"
    id: int = Field(primary_key=True, sa_column_kwargs={"autoincrement": True})
    id_chave: UUID = Field(sa_column=Column(PG_UUID(as_uuid=True), nullable=False, unique=True, server_default=text("gen_random_uuid()")))
    entidade: str = Field(sa_column=Column(type_=String, nullable=True))
    ativo: bool = Field(sa_column=Column(Boolean, nullable=False, server_default=text("true")))
    data_criacao: datetime = Field(sa_column=Column(type_=DateTime, nullable=False, server_default=text("now()")))
    data_alteracao: datetime = Field(sa_column=Column(type_=DateTime, nullable=False, server_default=text("now()")))

    contas_rel: Optional[List["Conta"]] = Relationship(back_populates="entidade_rel") # type: ignore
    recibos_rel: Optional[List["Recibo"]] = Relationship(back_populates="entidade_rel") # type: ignore
    faturas_cabec_rel: Optional[List["FaturaCabec"]] = Relationship(back_populates="entidade_rel") # type: ignore
    comprovativos_rel: Optional[List["Comprovativo"]] = Relationship(back_populates="entidade_rel") # type: ignore
    comprova_padroes_rel: Optional[List["ComprovaPadrao"]] = Relationship(back_populates="entidade_rel") # type: ignore

class EntidadeCreate(SQLModel):
    id: Optional[int] = Field(default=None)
    entidade: str
    ativo: Optional[bool] = Field(default=True)


    
    @field_validator("entidade", mode="before")
    def lower(cls, valor: Optional[str]):
        if valor is not None:
            return valor.lower()
        return valor
    
    class Config:
        json_schema_extra = {
            "example": {
                "entidade": "teste_entidade",
                "ativo": True
            }
        }

class EntidadeUpdate(SQLModel):
    entidade: Optional[str] = Field(default=None)
    ativo: Optional[bool] = Field(default=None)


class EntidadeBase(SQLModel):
    id: int = Field(default=None)
    id_chave: UUID = Field(default=None)
    entidade: str = Field(default=None)
    ativo: bool = Field(default=None)
    data_criacao: datetime = Field(default=None)
    data_alteracao: datetime = Field(default=None)
