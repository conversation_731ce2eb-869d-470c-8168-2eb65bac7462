from datetime import datetime
from typing import Optional
from uuid import UUID
from pydantic import BaseModel, Field


class Teste(BaseModel):
    id: Optional[int] = Field(default=None)
    id_chave: Optional[UUID] = Field(default=None)
    campo1: Optional[str] = Field(default=None)
    campo2: Optional[str] = Field(default=None)
    campo3: Optional[str] = Field(default=None)
    data_criacao: Optional[datetime] = Field(default=None)
    data_alteracao: Optional[datetime] = Field(default=None)


    def dict(self, *args, **kwargs) -> dict:
        original_dict = super().model_dump(*args, **kwargs)
        for key, value in original_dict.items():
            if isinstance(value, datetime):
                original_dict[key] = value.isoformat()
        return original_dict
    

