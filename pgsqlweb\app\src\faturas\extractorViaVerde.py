import datetime
from src.services.log import Logger, get_funcao
from src.services.file_service import FileService
from src.faturas.classFatura import <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>

logger = Logger()

tabela = 'via_verde'

class FaturaViaVerdeExtractor:
    def __init__(self, pdf) -> None:
        self.file = FileService(file=pdf)
        try:
            self.dados = self.file.ler_xml()
        except ValueError as e:
            logger.log_info(message={'error' : e}, tabela=tabela, funcao=get_funcao())
    
    def extrair_fatura(self) -> str:
        funcao = get_funcao()
        extracto = self.dados.get('EXTRACTO', {}) # type: ignore
        value = extracto.get('@id', {}) 
        message = {'valor': value,}
        logger.log_info(message=message, tabela=tabela, funcao=funcao)
        return str(value)
    
    def extrair_nif(self) -> str:
        funcao = get_funcao()
        value = self.dados.get('EXTRACTO', {}).get('CLIENTE', {}).get('NIF') # type: ignore
        message = {'valor': value,}
        logger.log_info(message=message, tabela=tabela, funcao=funcao)
        return str(value)
    
    

    def extrair_valor(self) -> float:
        funcao = get_funcao()
        value = self.dados.get('EXTRACTO', {}).get('TOTAL', {}) # type: ignore
        value = float(value.replace(',', '.'))
        message = {'valor': value,}
        logger.log_info(message=message, tabela=tabela, funcao=funcao)
        return value
    
    def extrair_iva(self) -> float:
        funcao = get_funcao()
        value = self.dados.get('EXTRACTO', {}).get('TOTAL_IVA', {}) # type: ignore
        value = float(value.replace(',', '.'))
        message = {'valor': value,}
        logger.log_info(message=message, tabela=tabela, funcao=funcao)
        return value

    def extrair_linhas(self) -> list:
        transacoes = self.dados.get('EXTRACTO').get('IDENTIFICADOR').get('TRANSACCAO') # type: ignore
        linhas = []
        for transacao in transacoes:
            valor = transacao.get('IMPORTANCIA')
            valor = float(valor.replace(',','.'))

            tx_iva = transacao.get('TAXA_IVA')
            tx_iva = float(tx_iva) / 100

            desconto = transacao.get('VALOR_DESCONTO')
            desconto = float(desconto.replace(',', '.'))

            base = round(valor / (1 + tx_iva),2)
            iva = round(valor - base, 2)

            entrada = transacao.get('ENTRADA', "")
            data_entrada = transacao.get('DATA_ENTRADA', "")
            hora_entrada = transacao.get('HORA_ENTRADA', "")
            saida = transacao.get('SAIDA', "")
            data_saida = transacao.get('DATA_SAIDA', "")
            hora_saida = transacao.get('HORA_SAIDA', "")

            if not data_entrada or data_entrada.lower() == "null": 
                data_entrada = data_saida
            if not hora_entrada or hora_entrada.lower() == "null":
                hora_entrada = hora_saida     

            data = f'{data_entrada} {hora_entrada}'
            data = datetime.datetime.strptime(data, '%d-%m-%Y %H:%M')
            artigo = f'{hora_entrada}: {entrada} ----- {hora_saida}: {saida}'
            valor_bruto = base + desconto  
            linha = Linhas(
                valor=valor,
                tx_iva=tx_iva,
                desconto=desconto,
                base=base,
                iva=iva,
                data=data,
                artigo=artigo,
                valor_bruto=valor_bruto
            )
            linhas.append(linha)
        return linhas




    def run_extraction(self) -> Fatura:
        fatura = self.extrair_fatura()
        valor = self.extrair_valor()
        iva = self.extrair_iva()
        nif = self.extrair_nif()
        base = valor - iva
        desconto = 0.0
        valor_bruto = base + desconto
        entidade_id = 14


        cabecalho = Cabecalho(
                                entidade_id=entidade_id,
                                fatura=fatura,
                                valor=valor,
                                iva=iva,
                                nif=nif,
                                base=base,
                                desconto=desconto,
                                valor_bruto=valor_bruto,
                            )


        linhas = self.extrair_linhas()
        max_date = max(linha.data for linha in linhas)
        for linha in linhas:
            linha.fatura = cabecalho.fatura
            linha.ano = max_date.year
            linha.mes = max_date.month


        cabecalho.data = max_date
        cabecalho.ano = cabecalho.data.year  # type: ignore
        cabecalho.mes = cabecalho.data.month     # type: ignore
        fatura_via_verde: Fatura = Fatura(cabecalho=cabecalho, linhas=linhas)

        
        return fatura_via_verde
    


