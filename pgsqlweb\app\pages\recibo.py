import streamlit as st
from streamlit_extras.switch_page_button import switch_page 

modos = ["-", "upload_supabase", "analise"]
utilizadores = ['ricardo', 'neide']

def main():
    st.set_page_config(layout="wide")
    with st.sidebar:
        modo = st.radio("Selecionar modo:", modos, index=0)

    if modo == "upload_supabase":
        with st.sidebar:
            selected_user = st.selectbox("Selecionar utilizador", ["-"] + utilizadores)
        if selected_user != "-":
            st.session_state['selected_user'] = selected_user

            try:
                import pages.recibos.recibos_supload as up
                up.main()
            except ModuleNotFoundError as e:
                st.error(e)

    elif modo == "analise":
        try:
            import pages.recibos.recibos_analise as an
            an.main()
        except ModuleNotFoundError as e:
            st.error(e)

if __name__ == "__main__":
    if not st.session_state.get('authenticated'):
        switch_page("main")
    else:
        if 'selected_user' not in st.session_state:
            st.session_state['selected_user'] = None
        main()