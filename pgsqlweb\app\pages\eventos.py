from src.contactos.classContacto import ContactoClean
import streamlit as st # type: ignore
import pandas as pd
from datetime import datetime
from typing import Optional
from src.comprovativos.classComprovativo import Comprovativo
from src.querys.classQuery import Query
from src.services.supabase_client import connect
from streamlit_extras.switch_page_button import switch_page  # type: ignore
from src.teste.classTeste import Teste

supabase = connect()

# Configuração da página

st.set_page_config(layout="wide")   

def initialize_session_state():
      
    if 'eventos' not in st.session_state:
        st.session_state['eventos'] = pd.DataFrame()
    if 'last_update_eventos' not in st.session_state:
        st.session_state['last_update_eventos'] = datetime(1900, 1, 1)


    if 'logs' not in st.session_state:
        st.session_state['logs'] = pd.DataFrame()
    if 'last_update_logs' not in st.session_state:
        st.session_state['last_update_logs'] = datetime(1900, 1, 1)

    if 'logs_app' not in st.session_state:
        st.session_state['logs_app'] = pd.DataFrame()
    if 'last_update_logs_app' not in st.session_state:
        st.session_state['last_update_logs_app'] = datetime(1900, 1, 1)




def load_eventos() -> Optional[pd.DataFrame]:
    try:
        query_result = Query.get_query_by_id(10)
        if query_result and query_result.query:
            df = Query.execute_query(querie=query_result.query)
            st.session_state['eventos'] = df
            st.session_state['last_update_eventos'] = datetime.now()
            return df
        st.error("Query inválida")
        return pd.DataFrame()
    except Exception as e:
        st.error(f"Erro: {str(e)}")
        return pd.DataFrame()

def load_logs() -> Optional[pd.DataFrame]:
    try:
        supabase = connect()
        response = supabase.table("logs").select("tabela, operacao, old, new").order('data', desc=True).limit(10).execute()
        df = pd.DataFrame(response.data)
        st.session_state['logs'] = df
        st.session_state['last_update_logs'] = datetime.now()
        return df
    except Exception as e:
        st.error(f"Erro ao carregar logs: {str(e)}")
        return pd.DataFrame()


def load_logs_app() -> Optional[pd.DataFrame]:
    try:
        supabase = connect()
        response = supabase.table("logs_app").select("*").order('data_criacao', desc=True).limit(10).execute()
        df = pd.DataFrame(response.data)
        st.session_state['logs_app'] = df
        st.session_state['last_update_logs_app'] = datetime.now()
        return df
    except Exception as e:
        st.error(f"Erro ao carregar logs: {str(e)}")
        return pd.DataFrame()




def display_dataframe(df: pd.DataFrame, last_update: datetime, key: str):
    index = None
    st.metric("last_update", last_update.strftime("%H:%M:%S"))
    df = df.reset_index(drop=True)
    selection = st.dataframe(df, selection_mode="single-row", on_select="rerun", hide_index=True, use_container_width=True)
    try:
        index = selection["selection"]["rows"][0] # type: ignore
    except IndexError:
        pass
    if index is not None:
        st.divider()
        col1, col2= st.columns([1, 1])
        linha = df.loc[index]
        with col1:
            st.write(f"tabela: {linha['tabela']}")
            st.write(f"tipo: {linha['tipo']}")
            st.write(f"estado: {linha['detalhe']}")
        with col2:
            st.write(f"id: {linha['fkid_chave']}")
            st.write(f"data_criacao: {linha['data_criacao'].strftime("%H:%M:%S")}")
            st.write(f"data_alteracao: {linha['data_alteracao'].strftime("%H:%M:%S")}")

        st.json(linha.to_dict()['new'])

def main():
    with st.sidebar:
        if st.button('inserir teste', key="inserir_teste"):
            teste = Teste(campo1='valor1', campo2='valor2')
            result = Teste.insert_teste(teste)
            if result:
                st.success("teste inserido!")
            else:
                st.error("Erro ao inserir.")

        if st.button('inserir contacto', key="inserir_contacto"):
            contacto = ContactoClean(
                                    nome='teste_nome',
                                    apelido='teste_apelido',
                                    telefones=[{'field': '999999999', 'label': 'mobile'}, {'field': '888888888', 'label': 'home'}],
                                    mails=[{'field': "teste@<EMAIL>", 'label': "WORK"}],
                                    origem='<EMAIL>'

                                    )   
            response = supabase.table("contactos").insert(contacto.dict(exclude_unset=True, exclude_none=True)).execute()
            if response.data:
                st.success("teste inserido!")
            else:
                st.error("Erro ao inserir.")


        if st.button('inserir_comprovativo', key="inserir_comprovativo"):
            exemplo = Comprovativo(
                                    data_valor=datetime.now(),
                                    data_movimento=datetime.now(),
                                    movimento='TRF CXDAPP',
                                    valor=999.99,
                                    tipo_movimento='D',
                                    conta='0507021920600'
                                )
            response = supabase.table("comprovativos").insert(exemplo.dict(exclude_unset=True, exclude_none=True)).execute()
            if response.data:
                st.success("comprovativo inserido!")
            else:
                st.error("Erro ao inserir.")

        if st.button('delete_eventos', key='delete_eventos'):
            supabase.table("eventos").delete().gte('estado', 0).execute()
            st.success("eventos_limpos")
        if st.button('delete_logs', key='delete_logs'):
            supabase.table("logs").delete().gte('id', 1).execute()
            st.success('logs_limpos')
        if st.button('delete_logs_app', key='delete_logs_app'):
            supabase.table("logs_app").delete().gte('id', 1).execute()
            st.success('logs_app_limpos')





    initialize_session_state()
    tab1, tab2, tab3, tab4 = st.tabs(["Eventos", "Logs", 'logs_app', 'diagrama'])

    with tab1:
        if st.button("update", type="primary", key="update_eventos"):
            with st.spinner("eventos..."):
                load_eventos()
        
        if not st.session_state['eventos'].empty:
            display_dataframe(st.session_state['eventos'], st.session_state['last_update_eventos'], "eventos")
        else:
            st.info("Nenhum evento carregado ainda.")


    with tab2:
        if st.button("update", type="primary", key="update_logs"):
            with st.spinner("logs..."):
                load_logs()

        if not st.session_state['logs'].empty:
            display_dataframe(st.session_state['logs'], st.session_state['last_update_logs'], "logs")
        else:
            st.info("Nenhum log carregado ainda.")

    with tab3:
        if st.button("update", type="primary", key="update_logs_app"):
            with st.spinner("logs_app..."):
                load_logs_app()

        if not st.session_state['logs_app'].empty:
            display_dataframe(st.session_state['logs_app'], st.session_state['last_update_logs_app'], "logs_app")
        else:
            st.info("Nenhum log carregado ainda.")


    with tab4:
        file = '_mind.drawio.svg'
        st.image(file)

if __name__ == "__main__":
    if not st.session_state.get('authenticated'):
        switch_page("main")
    else:
        main()