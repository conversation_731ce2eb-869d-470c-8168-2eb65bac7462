{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>data_valor</th>\n", "      <th>data_movimento</th>\n", "      <th>movimento</th>\n", "      <th>valor</th>\n", "      <th>segmento</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-08-22</td>\n", "      <td>2023-08-24 00:00:00</td>\n", "      <td>COMPRAS C.DEB LUBRICE</td>\n", "      <td>-31.90</td>\n", "      <td>combustivel</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-05-10</td>\n", "      <td>2024-05-13 00:00:00</td>\n", "      <td>COMPRAS C.DEB LUBRICE</td>\n", "      <td>-11.80</td>\n", "      <td>tabaco</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2021-05-30</td>\n", "      <td>2021-05-30 22:05:00</td>\n", "      <td>COMPRA SAND</td>\n", "      <td>-14.00</td>\n", "      <td>restauracao</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-04-22</td>\n", "      <td>2024-04-24 00:00:00</td>\n", "      <td>COMPRAS C.DEB LUBRICE</td>\n", "      <td>-35.85</td>\n", "      <td>combustivel</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-09-12</td>\n", "      <td>2024-09-14 00:00:00</td>\n", "      <td>COMPRAS C.DEB LUBRICE</td>\n", "      <td>-5.90</td>\n", "      <td>tabaco</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3136</th>\n", "      <td>2025-03-08</td>\n", "      <td>2025-03-08 00:00:00</td>\n", "      <td>TFI PAULA MARIA JESUS</td>\n", "      <td>20.00</td>\n", "      <td>transferencia</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3137</th>\n", "      <td>2025-02-16</td>\n", "      <td>2025-02-18 00:00:00</td>\n", "      <td>COMPRAS C.DEB LUBRICE</td>\n", "      <td>-3.20</td>\n", "      <td>cafe_bar</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3138</th>\n", "      <td>2025-02-27</td>\n", "      <td>2025-03-01 00:00:00</td>\n", "      <td>COMPRAS C.DEB LUBRICE</td>\n", "      <td>-5.70</td>\n", "      <td>tabaco</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3139</th>\n", "      <td>2025-02-28</td>\n", "      <td>2025-03-03 00:00:00</td>\n", "      <td>COMPRAS C.DEB LUBRICE</td>\n", "      <td>-5.70</td>\n", "      <td>tabaco</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3140</th>\n", "      <td>2025-03-03</td>\n", "      <td>2025-03-05 00:00:00</td>\n", "      <td>COMPRAS C.DEB LUBRICE</td>\n", "      <td>-35.90</td>\n", "      <td>combustivel</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3141 rows × 5 columns</p>\n", "</div>"], "text/plain": ["     data_valor      data_movimento              movimento   valor  \\\n", "0    2023-08-22 2023-08-24 00:00:00  COMPRAS C.DEB LUBRICE  -31.90   \n", "1    2024-05-10 2024-05-13 00:00:00  COMPRAS C.DEB LUBRICE  -11.80   \n", "2    2021-05-30 2021-05-30 22:05:00            COMPRA SAND  -14.00   \n", "3    2024-04-22 2024-04-24 00:00:00  COMPRAS C.DEB LUBRICE  -35.85   \n", "4    2024-09-12 2024-09-14 00:00:00  COMPRAS C.DEB LUBRICE   -5.90   \n", "...         ...                 ...                    ...     ...   \n", "3136 2025-03-08 2025-03-08 00:00:00  TFI PAULA MARIA JESUS   20.00   \n", "3137 2025-02-16 2025-02-18 00:00:00  COMPRAS C.DEB LUBRICE   -3.20   \n", "3138 2025-02-27 2025-03-01 00:00:00  COMPRAS C.DEB LUBRICE   -5.70   \n", "3139 2025-02-28 2025-03-03 00:00:00  COMPRAS C.DEB LUBRICE   -5.70   \n", "3140 2025-03-03 2025-03-05 00:00:00  COMPRAS C.DEB LUBRICE  -35.90   \n", "\n", "           segmento  \n", "0       combustivel  \n", "1            tabaco  \n", "2       restauracao  \n", "3       combustivel  \n", "4            tabaco  \n", "...             ...  \n", "3136  transferencia  \n", "3137       cafe_bar  \n", "3138         tabaco  \n", "3139         tabaco  \n", "3140    combustivel  \n", "\n", "[3141 rows x 5 columns]"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "from src.querys.classQuery import Query\n", "\n", "df = Query.executa_query(1, {'utilizador': ('ricardo',)})\n", "df\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.0"}}, "nbformat": 4, "nbformat_minor": 2}