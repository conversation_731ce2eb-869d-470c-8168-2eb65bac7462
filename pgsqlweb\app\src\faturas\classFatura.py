from datetime import datetime
from typing import Any, List, Optional
from uuid import UUID
import pandas as pd
from pydantic import BaseModel, field_validator


class Cabecalho(BaseModel):
    id_chave: Optional[UUID] = None
    data : Optional[datetime] = None
    entidade_id : Optional[int] = None
    fatura : Optional[str] = None
    valor_bruto: Optional[float] = 0
    desconto: Optional[float] = 0
    base: Optional[float] = 0
    iva: Optional[float] = 0
    valor: Optional[float] = 0
    nif : Optional[str] = None
    nif_entidade : Optional[str] = None
    ano: Optional[int] = None
    mes: Optional[int] = None
    link : Optional[str] = None
    data_criacao: Optional[datetime] = None
    data_alteracao: Optional[datetime] = None



    def dict(self, *args, **kwargs) -> dict[str, Any]:
        dados = super().model_dump(*args, **kwargs)
        for key, value in dados.items():
            if isinstance(value, datetime):
                dados[key] = value.isoformat()
        return dados
    

    @classmethod
    def get_tabela(cls, result: List[Any]) -> pd.DataFrame:
        dados = []
        for s in result:
            dados.append(s.model_dump())
        df = pd.DataFrame(dados)
        return df
    

class Linhas(BaseModel):
    id_chave_doc: Optional[UUID] = None
    data: Optional[datetime] = None
    fatura: Optional[str] = None
    artigo: Optional[str] = None
    valor_bruto: Optional[float] = 0
    desconto: Optional[float] = 0
    base: Optional[float] = 0
    iva: Optional[float] = 0
    valor: Optional[float] = 0
    tx_iva: Optional[float] = None
    ano: Optional[int] = None
    mes: Optional[int] = None
    id_chave: Optional[UUID] = None
    data_criacao: Optional[datetime] = None
    data_alteracao: Optional[datetime] = None


    @field_validator("id_chave_doc", mode="before")
    def converter_para_uuid(cls, valor):
        return UUID(valor) if isinstance(valor, str) else valor

    def dict(self, *args, **kwargs) -> dict[str, Any]:
        dados = super().model_dump(*args, **kwargs)
        for key, value in dados.items():
            if isinstance(value, datetime):
                dados[key] = value.isoformat()
        return dados
    

    @classmethod
    def get_tabela(cls, result: List[Any]) -> pd.DataFrame:
        dados = []
        for s in result:
            dados.append(s.model_dump())
        df = pd.DataFrame(dados)
        return df
    




class Fatura(BaseModel):
    cabecalho: Cabecalho
    linhas: List[Linhas]

    def criar_dfs(self) -> tuple[pd.DataFrame, pd.DataFrame]:
        df_cabec = pd.DataFrame([self.cabecalho.dict(exclude_unset=True)])
        df_linhas = pd.DataFrame([linha.dict(exclude_unset=True) for linha in self.linhas])
        return df_cabec, df_linhas