import streamlit as st
import json
from typing import Any
from streamlit_extras.switch_page_button import switch_page  # type: ignore[import]
from pyicloud import PyiCloudService
from src.contactos.classContacto import ContactoRaw, ContactoClean
from src.services.supabase_client import connect

supabase = connect()

st.set_page_config(layout="wide", page_title="Login", page_icon="☁️")
st.title("iCloud")





if "email" not in st.session_state:
    st.session_state.email = None
if "api_login" not in st.session_state:
    st.session_state.api_login = None
if "logged" not in st.session_state:
    st.session_state.logged = False
if "requires_2fa" not in st.session_state:
    st.session_state.requires_2fa = False
if "code" not in st.session_state:
    st.session_state.code = ""
if "login_clicked" not in st.session_state:
    st.session_state.login_clicked = False

api_login: Any
contactos_clean: list[<PERSON><PERSON><PERSON>lean] = []
data_to_insert: list[Any] = []


def logout():
    st.session_state.email = None
    st.session_state.api_login = None
    st.session_state.logged = False
    st.session_state.requires_2fa = False
    st.session_state.code = ""
    st.session_state.login_clicked = False

def authenticate(email: str, password: str):
    try:
        st.session_state.api_login = None
        st.session_state.logged = False
        st.session_state.requires_2fa = False
        api_login = PyiCloudService(email, password)
        
        if api_login.requires_2fa:
            st.warning("2fa required")
            st.session_state.requires_2fa = True
            st.session_state.logged = False
            st.session_state.api_login = api_login
        else:
            st.session_state.api_login = api_login
            st.session_state.logged = True
    except Exception as e:
        st.error(e)




def valida_2fa():
    try:
        api_login = st.session_state.api_login
        if api_login and api_login.validate_2fa_code(st.session_state.code):
            st.session_state.logged = True
            st.session_state.requires_2fa = False
        else:
            st.error("2fa invalid.")
    except Exception as e:
        st.error(f"validate error 2fa: {e}")



def main():
    #########################################################################################################
    col1, col2, col3, col4, col5 = st.columns([1.2, 1, 0.5, 0.5, 0.5])
    with col1:
        email = st.text_input("email", placeholder="insert email")

    #########################################################################################################    
    with col2:
        password = st.text_input("password", type="password", placeholder="insert password")

    #########################################################################################################
    with col3:
        st.write("")
        st.write("")
        if st.button("login"):
            st.session_state.login_clicked = True

    if st.session_state.login_clicked is True:
        if email and password:
            authenticate(email, password)
            st.session_state.email = email
        else:
            st.warning("inserir email e password.")
            st.session_state.login_clicked = False    
        st.session_state.login_clicked = False  


    #########################################################################################################
    if st.session_state.requires_2fa is True:
        st.warning("2fa auth")
        st.session_state.code = st.text_input("code 2fa", max_chars=6)
        if st.button("validate"):
            valida_2fa()

    #########################################################################################################
    with col4:
        if st.session_state.logged is True:
            st.write("")
            st.success("logged")
        else:
            st.write("")
            st.warning("not logged")

    with col5:
        if st.session_state.logged is True:
            st.write("")
            st.button("logout", on_click=logout)



    #########################################################################################################
    if st.session_state.logged is True:
        mail = st.session_state.email
        api = st.session_state.api_login

        if st.button("update"):
            if api:
                read_contactos = api.contacts.all()
                # por cada contacto, o id_chave é verificado se existe na BD 
                for index, contacto in enumerate(read_contactos):
                    # if index >= 5:
                    #     break
                    contacto_raw = ContactoRaw(**contacto)
                    contacto_clean = ContactoClean.clean(contacto_raw)
                    contacto_clean.origem = mail
                    raw_dict = contacto_raw.model_dump_json(exclude_unset=True, exclude_none=True)
                    clean_dict = contacto_clean.model_dump_json(exclude_unset=True, exclude_none=True)


                    # VERIFICA SE O CONTACTO EXISTE
                    response = supabase.table("contactos").select("*").eq("id_chave", contacto_clean.id_chave).execute()
                    if not response.data:        
                        response = supabase.table("contactos").insert(json.loads(clean_dict)).execute()
                        if response.data:
                            st.success(f"Contato inserido: {contacto_clean.nome} {contacto_clean.apelido}")
                        else:
                            st.error(f"Erro ao inserir: {contacto_clean.id_chave}.")

                    elif response.data:
                        existing_contact = ContactoClean(**response.data[0])
                        # with st.expander(f'JÁ EXISTE: {contacto_clean.nome} {contacto_clean.apelido}'):
                        #     col1, col2, col3 = st.columns(3)
                        #     with col1:
                        #         st.subheader(f"Contacto Raw")
                        #         st.json(raw_dict)
                        #     with col2:
                        #         st.subheader(f"Contacto Clean")
                        #         st.json(clean_dict)
                        #     with col3:
                        #         st.subheader(f"Contacto Base Dados")
                        #         st.json(existing_contact.model_dump_json(exclude_unset=True, exclude_none=True))
                        changes_detected = False
                        fields_to_check = ['nome', 'apelido', 'telefones', 'mails', 'entidade', 'origem']
                        for field in fields_to_check:
                            if getattr(contacto_clean, field, None) != getattr(existing_contact, field, None):
                                changes_detected = True
                                break
                        if changes_detected:
                            response = supabase.table("contactos").update(json.loads(clean_dict)).eq("id_chave", contacto_clean.id_chave).execute()
                            if response.data:
                                st.success(f"Contato atualizado: {contacto_clean.nome} {contacto_clean.apelido}")
                            else:
                                st.error(f"Erro ao atualizar: {contacto_clean.id_chave}.")



if __name__ == "__main__":
    if not st.session_state.get('authenticated'):
        switch_page("main")
    else:
        main()
