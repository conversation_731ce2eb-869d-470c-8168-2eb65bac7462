from typing import List, Optional
from sqlmodel import Session
from fastapi import APIRouter, Depends, Query
from comum.models.comprovativos import ComprovativoBase, ComprovativoCreate, ComprovativoUpdate
from comum.services.logger import setup_logger
from app.database.postgres import get_session
from app.workers.comprovativos import ComprovativosWorker
from app.extractors.comprovativos import ComprovativoExtractor
logger = setup_logger()

comprovativos_router = APIRouter()

def get_worker(session: Session = Depends(get_session)) -> ComprovativosWorker:
    return ComprovativosWorker(session)


@comprovativos_router.get("/comprovativos", response_model=List[ComprovativoBase])
async def lista_completa(
                            worker: ComprovativosWorker = Depends(get_worker), 
                            ativo: Optional[bool] = Query(None), 
                            movimento: Optional[str] = Query(None),
                            conta_id: Optional[int|List[int]] = Query(None)
                            ):
    return list(worker.lista(ativo, movimento, conta_id))


@comprovativos_router.get("/comprovativos/{id}", response_model=ComprovativoBase)
async def procura(id: int, worker: ComprovativosWorker = Depends(get_worker)):
    return worker.procura(id)


@comprovativos_router.post("/comprovativos", response_model=ComprovativoBase)
async def cria(novos_dados: ComprovativoCreate, worker: ComprovativosWorker = Depends(get_worker)):
    return worker.cria(novos_dados)


@comprovativos_router.patch("/comprovativos/{id}", response_model=ComprovativoBase)
async def atualiza(id: int, dados: ComprovativoUpdate, worker: ComprovativosWorker = Depends(get_worker)):
    return worker.atualiza(id, dados)
    

@comprovativos_router.delete("/comprovativos/{id}")
async def remove(id: int, worker: ComprovativosWorker = Depends(get_worker)):
    worker.remove(id)
    return {"message": f"ID: **{id}** deleted!"}


@comprovativos_router.post("/comprovativos/extrair", response_model=ComprovativoBase, response_model_exclude_none=True, response_model_exclude_unset=True)
async def extract(texto: str, nome_ficheiro: str):
    extractor = ComprovativoExtractor(texto, nome_ficheiro)
    return extractor.run_extraction()


