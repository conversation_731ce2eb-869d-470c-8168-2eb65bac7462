from sqlmodel import SQLModel, Field, Relationship
from typing import Optional, List, Any
from datetime import datetime, date
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy import Column, text, Boolean, DateTime, String, Float, Integer
from uuid import UUID


class Comprovativo(SQLModel, table=True):
    __tablename__ = "comprovativos"
    id: int = Field(primary_key=True, sa_column_kwargs={"autoincrement": True})
    id_chave: UUID = Field(sa_column=Column(PG_UUID(as_uuid=True), nullable=False, unique=True, server_default=text("gen_random_uuid()")))
    data_valor: datetime = Field(sa_column=Column(type_=DateTime, nullable=False))
    data_movimento: datetime = Field(sa_column=Column(type_=DateTime, nullable=False))
    movimento: str = Field(sa_column=Column(type_=String, nullable=False))
    valor: float = Field(sa_column=Column(type_=Float, nullable=False))
    tipo_movimento: str = Field(sa_column=Column(type_=String, nullable=False))
    entidade: str = Field(sa_column=Column(type_=String, nullable=True))
    numero_cartao: str = Field(sa_column=Column(type_=String, nullable=True))
    tipo_pagamento: str = Field(sa_column=Column(type_=String, nullable=True))
    id_sibs: str = Field(sa_column=Column(type_=String, nullable=True))
    ordenante: str = Field(sa_column=Column(type_=String, nullable=True))
    conta_destino: str = Field(sa_column=Column(type_=String, nullable=True))
    origem_operacao: str = Field(sa_column=Column(type_=String, nullable=True))
    id_transferencia: str = Field(sa_column=Column(type_=String, nullable=True))
    local: str = Field(sa_column=Column(type_=String, nullable=True))
    swift_destinatario: str = Field(sa_column=Column(type_=String, nullable=True))
    iban_destinatario: str = Field(sa_column=Column(type_=String, nullable=True))
    motivo_sepa: str = Field(sa_column=Column(type_=String, nullable=True))
    observacao: str = Field(sa_column=Column(type_=String, nullable=True))
    ano: int = Field(sa_column=Column(type_=Integer, nullable=True))
    mes: int = Field(sa_column=Column(type_=Integer, nullable=True))
    link: str = Field(sa_column=Column(type_=String, nullable=True))
    cont_trf: str = Field(sa_column=Column(type_=String, nullable=True))
    conta_id: int = Field(foreign_key="contas.id")
    segmento_id: int = Field(foreign_key="segmentos.id")
    entidade_id: int = Field(foreign_key="entidades.id", nullable=True)
    ativo: bool = Field(sa_column=Column(Boolean, nullable=False, server_default=text("true")))
    data_criacao: datetime = Field(sa_column=Column(type_=DateTime, nullable=False, server_default=text("now()")))
    data_alteracao: datetime = Field(sa_column=Column(type_=DateTime, nullable=False, server_default=text("now()")))

    segmento_rel: Optional["Segmento"] = Relationship(back_populates="comprovativos_rel") # type: ignore
    entidade_rel: Optional["Entidade"] = Relationship(back_populates="comprovativos_rel") # type: ignore
    conta_rel: Optional["Conta"] = Relationship(back_populates="comprovativos_rel")       # type: ignore


class ComprovativoCreate(SQLModel):
    id: Optional[int] = Field(default=None)
    id_chave: Optional[UUID] = Field(default=None)
    data_valor: Optional[datetime] = Field(default=None)
    data_movimento: Optional[datetime] = Field(default=None)
    movimento: Optional[str] = Field(default=None)
    valor: Optional[float] = Field(default=None)
    tipo_movimento: Optional[str] = Field(default=None)
    entidade: Optional[str] = Field(default=None)
    numero_cartao: Optional[str] = Field(default=None)
    tipo_pagamento: Optional[str] = Field(default=None)
    id_sibs: Optional[str] = Field(default=None)
    ordenante: Optional[str] = Field(default=None)
    conta_destino: Optional[str] = Field(default=None)
    origem_operacao: Optional[str] = Field(default=None)
    id_transferencia: Optional[str] = Field(default=None)
    local: Optional[str] = Field(default=None)
    swift_destinatario: Optional[str] = Field(default=None)
    iban_destinatario: Optional[str] = Field(default=None)
    motivo_sepa: Optional[str] = Field(default=None)
    observacao: Optional[str] = Field(default=None)
    ano: Optional[int] = Field(default=None)
    mes: Optional[int] = Field(default=None)
    link: Optional[str] = Field(default=None)
    cont_trf: Optional[str] = Field(default=None)
    conta_id: int = 0
    segmento_id: int = 0
    entidade_id: int = 0
    ativo: Optional[bool] = Field(default=True)

    class Config:
        json_schema_extra = {
            "example": {
                "data_valor": "2000-01-01T00:00:00",
                "data_movimento": "2000-01-01T00:00:00",
                "movimento": "teste_movimento",
                "valor": 111.11,
                "tipo_movimento": "C",
                "entidade": "teste_entidade",
                "numero_cartao": "teste_numero_cartao",
                "tipo_pagamento": "teste_tipo_pagamento",
                "id_sibs": "teste_id_sibs",
                "ordenante": "teste_ordenante",
                "conta_destino": "teste_conta_destino",
                "origem_operacao": "teste_origem_operacao",
                "id_transferencia": "teste_id_transferencia",
                "local": "teste_local",
                "swift_destinatario": "teste_swift_destinatario",
                "iban_destinatario": "teste_iban_destinatario",
                "motivo_sepa": "teste_motivo_sepa",
                "observacao": "teste_observacao",
                "ano": 2000,
                "mes": 1,
                "link": "teste_link",
                "cont_trf": "teste_cont_trf",
                "conta_id": 0,
                "segmento_id": 0,
                "entidade_id": 0,
                "ativo": True
            }
        }


class ComprovativoUpdate(SQLModel):
    data_valor: Optional[datetime] = Field(default=None)
    data_movimento: Optional[datetime] = Field(default=None)
    movimento: Optional[str] = Field(default=None)
    valor: Optional[float] = Field(default=None)
    tipo_movimento: Optional[str] = Field(default=None)
    entidade: Optional[str] = Field(default=None)
    numero_cartao: Optional[str] = Field(default=None)
    tipo_pagamento: Optional[str] = Field(default=None)
    id_sibs: Optional[str] = Field(default=None)
    ordenante: Optional[str] = Field(default=None)
    conta_destino: Optional[str] = Field(default=None)
    origem_operacao: Optional[str] = Field(default=None)
    id_transferencia: Optional[str] = Field(default=None)
    local: Optional[str] = Field(default=None)
    swift_destinatario: Optional[str] = Field(default=None)
    iban_destinatario: Optional[str] = Field(default=None)
    motivo_sepa: Optional[str] = Field(default=None)
    observacao: Optional[str] = Field(default=None)
    ano: Optional[int] = Field(default=None)
    mes: Optional[int] = Field(default=None)
    link: Optional[str] = Field(default=None)
    cont_trf: Optional[str] = Field(default=None)
    conta_id: Optional[int] = Field(default=None)
    segmento_id: Optional[int] = Field(default=None)
    entidade_id: Optional[int] = Field(default=None)
    ativo: Optional[bool] = Field(default=None)


class ComprovativoBase(SQLModel):
    id: int = Field(default=None)
    id_chave: UUID = Field(default=None)
    data_valor: Optional[datetime] = Field(default=None)
    data_movimento: Optional[datetime] = Field(default=None)
    movimento: Optional[str] = Field(default=None)
    valor: Optional[float] = Field(default=None)
    tipo_movimento: Optional[str] = Field(default=None)
    entidade: Optional[str] = Field(default=None)
    numero_cartao: Optional[str] = Field(default=None)
    tipo_pagamento: Optional[str] = Field(default=None)
    id_sibs: Optional[str] = Field(default=None)
    ordenante: Optional[str] = Field(default=None)
    conta_destino: Optional[str] = Field(default=None)
    origem_operacao: Optional[str] = Field(default=None)
    id_transferencia: Optional[str] = Field(default=None)
    local: Optional[str] = Field(default=None)
    swift_destinatario: Optional[str] = Field(default=None)
    iban_destinatario: Optional[str] = Field(default=None)
    motivo_sepa: Optional[str] = Field(default=None)
    observacao: Optional[str] = Field(default=None)
    ano: Optional[int] = Field(default=None)
    mes: Optional[int] = Field(default=None)
    link: Optional[str] = Field(default=None)
    cont_trf: Optional[str] = Field(default=None)
    conta_id: Optional[int] = Field(default=None)
    segmento_id: Optional[int] = Field(default=None)
    entidade_id: Optional[int] = Field(default=None)
    ativo: Optional[bool] = Field(default=True)
    data_criacao: Optional[datetime] = Field(default=None)
    data_alteracao: Optional[datetime] = Field(default=None)





class ComprovativoNGX(SQLModel):
    id: int = Field(default=None)
    correspondent: Optional[str] = Field(default=None)
    document_type: Optional[int] = Field(default=None)
    storage_path: Optional[str] = Field(default=None)
    title: Optional[str] = Field(default=None)
    content: Optional[str] = Field(default=None)
    tags: Optional[List[Any]] = Field(default=None)
    created: Optional[datetime] = Field(default=None)
    created_date: Optional[date] = Field(default=None)
    modified: Optional[datetime] = Field(default=None)
    added: Optional[datetime] = Field(default=None)
    deleted: Optional[datetime] = Field(default=None)
    archive_serial_number: Optional[int] = Field(default=None)
    original_file_name: Optional[str] = Field(default=None)
    archive_file_name: Optional[str] = Field(default=None)
    owner: Optional[int] = Field(default=None)
    user_can_change: Optional[bool] = Field(default=None)
    notes: Optional[List[Any]] = Field(default=None)
    custom_fields: Optional[List[Any]] = Field(default=None)
    page_count: Optional[int] = Field(default=None)
    mime_type: Optional[str] = Field(default=None)
    

    
    