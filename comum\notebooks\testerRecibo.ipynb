{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import base64\n", "from src.recibos.classRecibo import Recibo\n", "from src.recibos.workerRecibo import WorkerRecibos\n", "from src.services.api import api_get\n", "from src.services.file_service import Ficheiro\n", "\n", "pdf_path = 'src/recibos/files/recibo_teste.pdf'\n", "\n", "response = WorkerRecibos().processar_ficheiro(ficheiro=pdf_path, utilizador='ricardo', save=True)\n", "print(response)\n", "recibo = Recibo(**response.json())\n", "\n", "response = api_get(tabela=f\"ficheiros/{recibo.id_chave}\")\n", "ficheiro = Ficheiro(**response.json())\n", "\n", "\n", "def gera_pdf(base64_pdf: str, nome_ficheiro: str = \"documento_recuperado.pdf\"):\n", "    try:\n", "        pdf_bytes = base64.b64decode(base64_pdf)\n", "        with open(nome_ficheiro, \"wb\") as f:\n", "            f.write(pdf_bytes)\n", "        print(f\"PDF criado com sucesso: {nome_ficheiro}\")\n", "    except Exception as e:\n", "        print(f\"Erro ao gerar PDF: {e}\")    \n", "\n", "gera_pdf(ficheiro.binario)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## TESTE STREAMLIT FILE"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import io\n", "\n", "with open(\"src/recibos/files/recibo_teste.pdf\", \"rb\") as f:\n", "    file_bytes = f.read()\n", "\n", "fake_uploaded_file = io.BytesIO(file_bytes)\n", "fake_uploaded_file.name = \"recibo_teste.pdf\"\n", "fake_uploaded_file.seek(0)\n", "response = WorkerRecibos().processar_ficheiro(ficheiro=fake_uploaded_file, utilizador='ricardo', save=True)\n", "recibo = Recibo(**response.json())\n", "\n", "response = api_get(tabela=f\"ficheiros/{recibo.id_chave}\")\n", "ficheiro = Ficheiro(**response.json())\n", "\n", "def gera_pdf(base64_pdf: str, nome_ficheiro: str = \"documento_recuperado.pdf\"):\n", "    try:\n", "        pdf_bytes = base64.b64decode(base64_pdf)\n", "        with open(nome_ficheiro, \"wb\") as f:\n", "            f.write(pdf_bytes)\n", "        print(f\"PDF criado com sucesso: {nome_ficheiro}\")\n", "    except Exception as e:\n", "        print(f\"Erro ao gerar PDF: {e}\")\n", "\n", "\n", "gera_pdf(ficheiro.binario)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 2}