from datetime import datetime
from typing import Optional
from uuid import UUID
from pydantic import BaseModel, Field

class Conta(BaseModel):
    id: Optional[int] = Field(default=None)
    id_chave: Optional[UUID] = Field(default=None)
    conta: str = Field(default=None)
    utilizador: str = Field(default=None)
    entidade_id: int = Field(default=None)
    ativo: bool = Field(default=True)
    data_criacao: Optional[datetime] = Field(default=None)
    data_alteracao: Optional[datetime] = Field(default=None)

    class Config:
        from_attributes = True
        populate_by_name = True
