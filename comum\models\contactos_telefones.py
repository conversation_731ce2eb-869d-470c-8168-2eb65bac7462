from sqlmodel import SQLModel, Field, Relationship
from typing import Optional
from datetime import datetime
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy import Column, text, Boolean, DateTime, String
from uuid import UUID


class ContactoTelefone(SQLModel, table=True):
    __tablename__ = "contactos_telefones"
    id: int = Field(primary_key=True, sa_column_kwargs={"autoincrement": True})
    id_chave: UUID = Field(sa_column=Column(PG_UUID(as_uuid=True), nullable=False, unique=True, server_default=text("gen_random_uuid()")))
    contacto_id: int = Field(foreign_key="contactos.id")
    telefone: str = Field(sa_column=Column(type_=String, nullable=True))
    tipo: str = Field(sa_column=Column(type_=String, nullable=True))
    cont_trf: str = Field(sa_column=Column(type_=String, nullable=True))
    ativo: bool = Field(sa_column=Column(Boolean, nullable=False, server_default=text("true")))
    data_criacao: datetime = Field(sa_column=Column(type_=DateTime, nullable=False, server_default=text("now()")))
    data_alteracao: datetime = Field(sa_column=Column(type_=DateTime, nullable=False, server_default=text("now()")))

    contacto_rel: Optional["Contacto"] = Relationship(back_populates="telefones_rel")

class ContactoTelefoneCreate(SQLModel):
    contacto_id: int
    telefone: str
    tipo: str
    cont_trf: str
    ativo: Optional[bool] = Field(default=True)

    class Config:
        json_schema_extra = {
            "example": {
                "contacto_id": 1,
                "telefone": "999999999",
                "tipo": "home",
                "cont_trf": "teste_cont_trf",
                "ativo": True
            }
        }


class ContactoTelefoneUpdate(SQLModel):
    telefone: Optional[str] = Field(default=None)
    tipo: Optional[str] = Field(default=None)
    cont_trf: Optional[str] = Field(default=None)
    ativo: Optional[bool] = Field(default=None)


class ContactoTelefoneBase(SQLModel):
    id: int = Field(default=None)
    id_chave: UUID = Field(default=None)
    telefone: str = Field(default=None)
    tipo: str = Field(default=None)
    cont_trf: str = Field(default=None)
    ativo: bool = Field(default=None)
    data_criacao: datetime = Field(default=None)
    data_alteracao: datetime = Field(default=None)
    ativo: bool = Field(default=None)
    data_criacao: datetime = Field(default=None)
    data_alteracao: datetime = Field(default=None)
