{"cells": [{"cell_type": "code", "execution_count": 2, "id": "dacf0b9e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["http://192.168.1.77:5000/api/v1/eventos/pendentes\n", "[]\n"]}], "source": ["from httpx import Client\n", "from comum.services.config import PGSQLAPI_URL\n", "\n", "\n", "try:\n", "    with <PERSON><PERSON>() as client:\n", "        url = f\"{PGSQLAPI_URL}eventos/pendentes\"\n", "        print(url)\n", "        response = client.get(url)\n", "        response.raise_for_status()\n", "        if response.status_code == 200:\n", "            print(response.json())\n", "        else:\n", "            print(f\"Nenhum evento pendente encontrado: {response.status_code} - {response.text}\")\n", "except Exception as e:\n", "    print(f\"Erro ao listar eventos pendentes: {str(e)}\")"]}, {"cell_type": "code", "execution_count": null, "id": "d8f3a09f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.0"}}, "nbformat": 4, "nbformat_minor": 5}