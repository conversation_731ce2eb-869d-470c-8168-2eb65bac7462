from datetime import datetime
import time
import threading
from src.teste.workerTeste import WorkerTeste
from src.comprovativos.workerComprovativo import WorkerComprovativos
from src.contactos.workerContacto import WorkerContacto
from src.eventos.classEvento import Evento
from src.services.api import api_update, api_eventos_pendentes
from src.services.logger import setup_logger
logger = setup_logger()


class WorkerEventos:
    def __init__(self):
        self.tabela = "eventos"
        self.ativo = False
        self.worker_teste = WorkerTeste()
        self.worker_comprovativos = WorkerComprovativos()
        self.worker_contacto = WorkerContacto()
        self.last_execucao = None


    def processar_evento(self, evento: Evento):
        try:
            if evento.tabela == 'teste':
                self.worker_teste.processar_evento(evento)

            elif evento.tabela == 'comprovativos':
                self.worker_comprovativos.processar_evento(evento)

            elif evento.tabela == 'contactos':
                self.worker_contacto.processar_evento(evento)
                
            else:
                self.set_estado(evento_id=evento.id, estado=100, obs=f"Tabela: {evento.tabela} não configurada")
            
        except Exception as e:
            self.set_estado(evento_id=evento.id, estado=99, obs='erro ao processar evento: '+ str(e))



    def get_pendentes(self):
        response = api_eventos_pendentes()
        if response.status_code == 200:
            return [Evento(**evento) for evento in response.json()]
        else:
            return []



    def set_estado(self, evento_id, estado, obs=""):
        api_update(tabela=self.tabela, data={"id": evento_id, "estado": estado, "obs": obs})



    def eventos_watcher(self):
        self.ativo = True
        while self.ativo:
            try:
                self.last_execucao = datetime.now()
                eventos = self.get_pendentes()
                if eventos:
                    for evento in eventos:
                        self.processar_evento(evento)
            except Exception as e:
                message = {'erro eventos watcher': str(e)}
                logger.error("Erro ao processar eventos", extra={"message": message})
            time.sleep(60)



    def iniciar(self):
        thread = threading.Thread(target=self.eventos_watcher, daemon=True, name="EventosWatcher")
        thread.start()
