from typing import List
from fastapi import HTTPException
from sqlmodel import Session, select
from sqlalchemy.exc import IntegrityError, OperationalError, DataError, ProgrammingError
from comum.services.logger import setup_logger
from comum.models.contas import Conta, ContaCreate, ContaUpdate, ContaBase
logger = setup_logger()

class ContasWorker:
    def __init__(self, session: Session):
        self.session = session

    def gestor_erros(self, e: Exception) -> None:
        if isinstance(e, IntegrityError):
            mensagem = str(e.orig).strip().split("\n")[0]
            logger.error(msg=f"{type(e).__name__} - {mensagem}", ThreadName="EstadosWorker", Application="pgsql-api", exc_info=e.orig)
            raise HTTPException(status_code=400, detail=f"Erro de integridade: {mensagem}")
        elif isinstance(e, OperationalError):
            mensagem = str(e.orig).strip().split("\n")[0]
            logger.error(msg=f"{type(e).__name__} - {mensagem}", ThreadName="EstadosWorker", Application="pgsql-api", exc_info=e.orig)
            raise HTTPException(status_code=500, detail=f"Erro no banco de dados: {mensagem}")
        elif isinstance(e, DataError):
            mensagem = str(e.orig).strip().split("\n")[0]
            logger.error(msg=f"{type(e).__name__} - {mensagem}", ThreadName="EstadosWorker", Application="pgsql-api", exc_info=e.orig)
            raise HTTPException(status_code=400, detail=f"Erro de dados inválidos: {mensagem}")
        elif isinstance(e, ProgrammingError):
            mensagem = str(e.orig).strip().split("\n")[0]
            logger.error(msg=f"{type(e).__name__} - {mensagem}", ThreadName="EstadosWorker", Application="pgsql-api", exc_info=e.orig)
            raise HTTPException(status_code=400, detail=f"Erro de programação SQL: {mensagem}")
        else:
            mensagem = str(e).strip().split("\n")[0]
            logger.error(msg=f"{type(e).__name__} - {mensagem}", ThreadName="EstadosWorker", Application="pgsql-api", exc_info=e)
            raise HTTPException(status_code=400, detail=f"Erro inesperado: {mensagem}")

    def lista(self) -> List[ContaBase]:
        try:
            query = select(Conta)
            return list(self.session.exec(query).all())
        except Exception as e:
            self.gestor_erros(e)
            raise  

    def procura(self, id: int) -> ContaBase:
        try:
            resultado = self.session.get(Conta, id)
            if not resultado:
                raise HTTPException(status_code=404, detail=f"ID: {id} não encontrado")
            return resultado
        except Exception as e:
            self.gestor_erros(e)
            raise


    def cria(self, dados: ContaCreate) -> Conta:
        try:
            novo = Conta(**dados.model_dump(exclude_unset=True, exclude_none=True))
            self.session.add(novo)
            self.session.commit()
            self.session.refresh(novo)
            logger.warning(msg=f"CONTA INSERT: {novo.id_chave}", ThreadName="ContasWorker", Application="pgsql-api", dados=novo.model_dump())
            return novo
        except Exception as e:
            self.gestor_erros(e)
            raise


    def atualiza(self, id: int, novos_dados: ContaUpdate) -> Conta:
        try:
            resultado = self.procura(id)
            novos_dados = novos_dados.model_dump(exclude_unset=True)
            for key, value in novos_dados.items():
                setattr(resultado, key, value)
            self.session.add(resultado)
            self.session.commit()
            self.session.refresh(resultado)
            logger.warning(msg=f"CONTA UPDATE: {resultado.id_chave}", ThreadName="ContasWorker", Application="pgsql-api", dados=novos_dados)
            return resultado
        except Exception as e:
            self.gestor_erros(e)
            raise


    def remove(self, id: int) -> None:
        try:
            resultado = self.procura(id)
            self.session.delete(resultado)
            self.session.commit()
            logger.warning(msg=f"CONTA DELETE: {resultado.id_chave}", ThreadName="ContasWorker", Application="pgsql-api", dados=resultado.model_dump())
        except Exception as e:
            self.gestor_erros(e)
            raise

