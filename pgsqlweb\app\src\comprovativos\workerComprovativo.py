import io
import zipfile
from src.comprovativos.classComprovativo import Comprovativo, ComprovativoPadrao
from src.contas.classConta import Conta
from src.services.api import api_create, api_update, api_get
import base64

class WorkerComprovativos:

    def atualizar_estado(self, evento_id, estado, obs=""):
        api_update(tabela="eventos", data={"estado": estado, "obs": obs, "id": evento_id})


    def processar_evento(self, evento):
        try:
            response = api_get(tabela=f'comprovativos/{evento.fkid}')
            comprovativo = Comprovativo(**response.json())

            response = api_get(tabela=f'contas/{comprovativo.conta_id}')
            conta = Conta(**response.json())

            response = api_get(tabela='comprova_padroes')
            padroes = [ComprovativoPadrao(**padrao) for padrao in response.json()]


            if evento.tipo == 'INSERT':
                for padrao in padroes:
                    if padrao.padrao.lower() in comprovativo.movimento.lower():
                        response = api_update(tabela='comprovativos', data={"segmento_id": padrao.segmento_id, "entidade_id": padrao.entidade_id, "id": comprovativo.id})
                if response.status_code == 200:
                    self.atualizar_estado(evento_id=evento.id, estado=200, obs=None)
                else:
                    self.atualizar_estado(evento_id=evento.id, estado=99, obs='update nao processado')


            elif evento.tipo == 'UPDATE':
                if conta.utilizador == 'ricardo':
                    if comprovativo.segmento_id == 4: # se for transferencia calcula o cont_trf
                        movimento_ultimos_9 = comprovativo.movimento[-9:]
                        comprovativo.cont_trf = movimento_ultimos_9[:3] + movimento_ultimos_9[-3:]
                        response = api_update(tabela='comprovativos', data={"cont_trf": comprovativo.cont_trf, "id": comprovativo.id})
                        if response.status_code == 200:
                            self.atualizar_estado(evento_id=evento.id, estado=200, obs=None)
                        else:
                            self.atualizar_estado(evento_id=evento.id, estado=99, obs='update nao processado')
                    else:
                        self.atualizar_estado(evento_id=evento.id, estado=100, obs='segmento sem condicoes')
                
                elif conta.utilizador == 'neide':
                    if comprovativo.segmento_id == 4:
                        comprovativo.cont_trf = comprovativo.movimento[-4:]
                        response = api_update(tabela='comprovativos', data={"cont_trf": comprovativo.cont_trf, "id": comprovativo.id})
                        if response.status_code == 200:
                            self.atualizar_estado(evento_id=evento.id, estado=200, obs=None)
                        else:
                            self.atualizar_estado(evento_id=evento.id, estado=99, obs='update nao processado')
                    else:
                        self.atualizar_estado(evento_id=evento.id, estado=100, obs='segmento sem condicoes')
                else:
                    self.atualizar_estado(evento_id=evento.id, estado=100, obs='utilizador sem condicoes')

            else:
                self.atualizar_estado(evento_id=evento.id, estado=100, obs='tipo de evento não configurado')


        except Exception as e:
            self.atualizar_estado(evento_id=evento.id, estado=99, obs=str(e))
            print(f"Erro ao processar evento {evento.id}: {e}")

    def _enviar_ficheiro(self, ficheiro, id_chave):
        if hasattr(ficheiro, 'read'):
            file_bin = ficheiro.read()
            try:
                ficheiro.seek(0)
            except:
                pass
        else:
            with open(ficheiro, 'rb') as f:
                file_bin = f.read()
        pdf_b64 = base64.b64encode(file_bin).decode('utf-8')
        payload = {
            "tabela": "comprovativos",
            "fkid_chave": str(id_chave),
            "binario": pdf_b64,
            "ativo": True
        }
        api_create(tabela="ficheiros", data=payload)

    def processar_ficheiro(self, ficheiro, utilizador, save=False):
        try:
            if utilizador == "ricardo":
                from src.comprovativos.extractorComprovativo import ComprovativoExtractor
                extraction = ComprovativoExtractor(pdf=ficheiro).run_extraction()
                if save:
                    response = api_create(tabela='comprovativos', data=extraction)
                    if response.status_code == 200:
                        comprovativo = response.json()
                        if hasattr(ficheiro, "seek"):
                            ficheiro.seek(0) 
                        self._enviar_ficheiro(ficheiro, comprovativo['id_chave'])
                        return response
                return extraction   

            # elif utilizador == "neide":
            #     from src.comprovativos.extractorComprovativoNeide import ComprovativoExtractorNeide
            #     extract_result = ComprovativoExtractorNeide(pdf=ficheiro).run_extraction()
            #     if save:
            #         extract_dict = [item.dict(exclude_unset=True, exclude_none=True) for item in extract_result]
            #         insert_result = self.supabase.table(self.tabela).insert(extract_dict).execute()
            #         return insert_result
            #     return extract_result # type: ignore
            else:
                raise Exception(f"utilizador:{utilizador} não implementado")
        except Exception as e:
            return None
        
    @staticmethod
    def create_zip(files):
        zip_buffer = io.BytesIO()
        with zipfile.ZipFile(zip_buffer, "w") as zf:
            for nome_arquivo, conteudo in files:
                zf.writestr(nome_arquivo, conteudo)
        zip_buffer.seek(0)
        return zip_buffer.getvalue()