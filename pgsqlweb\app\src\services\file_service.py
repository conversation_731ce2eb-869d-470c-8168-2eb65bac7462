from typing import Union, Optional
import pdfplumber
import PyPDF2
import xmltodict
from pydantic import BaseModel, ConfigDict, Field
from datetime import datetime
from uuid import UUID
from io import BytesIO
from src.services.log import get_funcao
from src.logs.classLogsApp import LogApp

tabela = 'FileService'
class FileService(BaseModel):
    file: Optional[Union[str, BytesIO]] = None

    # Configuração para permitir tipos arbitrários como BytesIO
    model_config = ConfigDict(arbitrary_types_allowed=True)

    def read_file(self) -> str:
        funcao = get_funcao()
        
        if self.file is None:
            message={'error' : 'Arquivo não fornecido'}
            LogApp.log_error(classe=tabela, operacao=funcao, resultado=message)
            raise ValueError("Arquivo não fornecido.")


        if isinstance(self.file, str):
            with open(self.file, 'r') as f:
                message={'origem' : 'local'}
                LogApp.log_info(classe=tabela, operacao=funcao, resultado=message)
                return f.read()
            
            
        elif isinstance(self.file, BytesIO):
            message={'origem' : 'online'}
            LogApp.log_info(classe=tabela, operacao=funcao, resultado=message)
            return self.file.getvalue().decode()
        else:
            raise ValueError("Tipo de arquivo inválido.")

    def extrair_texto_pdfplumber(self) -> str:
        funcao = get_funcao()
        if self.file is None:
            message={'error' : 'Arquivo não fornecido'}
            LogApp.log_error(classe=tabela, operacao=funcao, resultado=message)
            raise ValueError("Arquivo não fornecido.")
        try:
            with pdfplumber.open(self.file) as pdf:
                texto = ''.join([page.extract_text() for page in pdf.pages if page.extract_text()])
                message={'texto' : texto}
                LogApp.log_info(classe=tabela, operacao=funcao, resultado=message)
        except Exception as e:
            message={'error' : e}
            LogApp.log_error(classe=tabela, operacao=funcao, resultado=message)
            raise ValueError(f"Erro ao ler o arquivo: {e}")
        return texto
    
    
    def extrair_texto_pypdf(self) -> str:
        funcao = get_funcao()

        if self.file is None:
            message={'error' : 'Arquivo não fornecido'}
            LogApp.log_error(classe=tabela, operacao=funcao, resultado=message)
            raise ValueError("Arquivo não fornecido")
        try:
            reader = PyPDF2.PdfReader(self.file)
            texto = ''.join(page.extract_text() for page in reader.pages)
            message={'texto' : texto}
            LogApp.log_info(classe=tabela, operacao=funcao, resultado=message)
        except Exception as e:
            message={'error' : e}
            LogApp.log_error(classe=tabela, operacao=funcao, resultado=message)
            raise ValueError("Erro ao ler o arquivo")
        return texto
    


    def ler_xml(self):
        funcao = get_funcao()
        if not self.file:
            message={'error' : 'Arquivo não fornecido'}
            LogApp.log_error(classe=tabela, operacao=funcao, resultado=message)
            return ValueError("Nenhum arquivo fornecido para leitura.")
        try:
            dados = xmltodict.parse(self.file)
            message={'dados' : dados}
            LogApp.log_info(classe=tabela, operacao=funcao, resultado=message)
            return dados
        except Exception as e:
            message={'error' : e}
            LogApp.log_error(classe=tabela, operacao=funcao, resultado=message)
            raise ValueError(f"Erro ao ler o arquivo: {e}")



class Ficheiro(BaseModel):
    id: Optional[int] = Field(default=None)
    id_chave: Optional[UUID] = Field(default=None)
    tabela: Optional[str] = Field(default=None)
    fkid_chave: Optional[UUID] = Field(default=None)
    binario: Optional[str] = Field(default=None)
    ativo: Optional[bool] = Field(default=True)
    data_criacao: Optional[datetime] = Field(default=None)
    data_alteracao: Optional[datetime] = Field(default=None)