import inspect
import logging
import os
import uuid
import pandas as pd
from dotenv import load_dotenv
load_dotenv()

def get_funcao() -> str:
    stack = inspect.stack()
    funcao = stack[1].function if len(stack) > 1 else 'funcao_desconhecida'
    return funcao

class Logger:
    _instance = None

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = super(Logger, cls).__new__(cls)
        return cls._instance

    def __init__(self, log_file='src/logs/log.log', level=logging.DEBUG):
        if not hasattr(self, 'logger'):  # Para garantir que só será inicializado uma vez
            self.log_file = log_file
            self.logger = logging.getLogger(__name__)
            self.logger.setLevel(level)
            
            # Define o formato do logger com data, hora, ID, nível e função
            formatter = logging.Formatter(
                '%(id)s - %(asctime)s - %(levelname)s - %(tabela)s - %(funcao)s - %(message)s',
                datefmt='%d-%m-%Y %H:%M:%S')

            # Verifica se o diretório existe, caso contrário, cria
            os.makedirs(os.path.dirname(self.log_file), exist_ok=True)

            if not self.logger.hasHandlers():
                # Configura o manipulador de arquivo
                file_handler = logging.FileHandler(self.log_file)
                file_handler.setFormatter(formatter)
                self.logger.addHandler(file_handler)

    def _get_id(self):
        return str(uuid.uuid4())

    def log_debug(self, message: dict, tabela: str, funcao: str):
        self.logger.debug(message, extra={
                                    'id': self._get_id(), 
                                    'tabela': tabela.upper(),
                                    'funcao': funcao.upper()
                                    })

    def log_info(self, message: dict, tabela: str, funcao: str):
        self.logger.info(message, extra={
                                    'id': self._get_id(), 
                                    'tabela': tabela.upper(),
                                    'funcao': funcao.upper()
                                    })

    def log_warning(self, message: dict, tabela: str, funcao: str):
        self.logger.warning(message, extra={
                                    'id': self._get_id(), 
                                    'tabela': tabela.upper(),
                                    'funcao': funcao.upper()
                                    })

    def log_error(self, message: dict, tabela: str, funcao: str):
        self.logger.error(message, extra={
                                    'id': self._get_id(), 
                                    'tabela': tabela.upper(),
                                    'funcao': funcao.upper()
                                    })

    def log_critical(self, message: dict, tabela: str, funcao: str):
        self.logger.critical(message, extra={
                                    'id': self._get_id(), 
                                    'tabela': tabela.upper(),
                                    'funcao': funcao.upper()
                                    })

    def read_logs(self) -> str:
        try:
            with open(self.log_file, 'r') as log_file:
                return log_file.read()
        except FileNotFoundError:
            self.logger.error(f"Log file '{self.log_file}' not found.")
            return ""
        except Exception as e:
            self.logger.error(f"An error occurred while reading the log file: {e}")
            return ""

    def clear_logs(self):
            with open(self.log_file, 'w') as log_file:
                log_file.write("") 
                return True



    def get_tabela(self):
        log_data = []
        logs = self.read_logs()
        log_lines = logs.strip().split('\n')
        for line in log_lines:
            parts = line.split(' - ')
            log_id = parts[0].strip()
            log_time = parts[1].strip()
            log_level = parts[2].strip()
            log_table = parts[3].strip()
            log_function = parts[4].strip()
            log_message = parts[5].strip()

            log_data.append({
                'id_chave': log_id,
                'data_criacao': log_time,
                'log_level': log_level,
                'tabela': log_table,
                'funcao': log_function,
                'message': log_message
            })

        df = pd.DataFrame(log_data)
        df['data_criacao'] = pd.to_datetime(df['data_criacao'], format='%d-%m-%Y %H:%M:%S', dayfirst=True)
        df = df.sort_values(by='data_criacao', ascending=False)

        return df
    


