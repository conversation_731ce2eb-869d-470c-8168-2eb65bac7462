from datetime import datetime
from typing import Any, List, Optional
import pandas as pd
import psycopg2
from pydantic import BaseModel
from src.services.supabase_client import SupabaseConfig, connect


supabase = connect()

class Query(BaseModel):
    id_chave: Optional[int] = None
    descricao : Optional[str] = None
    query : Optional[str] = None
    data_criacao: Optional[datetime] = None
    data_alteracao : Optional[datetime] = None

        

    @classmethod
    def all_querys(cls) -> List["Query"]:
        try:
            response = supabase.table('querys').select("*").order('id_chave', desc=False).execute()
            registros = response.data
            if registros:
                return [cls(**reg) for reg in registros]
            else:
                return [] 
        except Exception as e:
            print(f"Erro: {e}")
            return []

    @classmethod
    def get_query_by_id(cls, id_chave: int) -> Optional["Query"]:
        all_queries = cls.all_querys()
        return next((q for q in all_queries if q.id_chave == id_chave), None)


    @classmethod
    def executa_query(cls, id_chave: int, query_params: Optional[dict] = None):
        try:
            query_selected = cls.get_query_by_id(id_chave=id_chave).query
            db_string = SupabaseConfig().db_string.get_secret_value()
            with psycopg2.connect(db_string) as conn:
                cur = conn.cursor()

                if query_params:
                    cur.execute(query_selected, query_params)
                else:
                    cur.execute(query_selected)

                if cur.description is None:
                    return pd.DataFrame()

                colnames = [desc[0] for desc in cur.description]
                rows = cur.fetchall()
                df = pd.DataFrame(rows, columns=colnames)
                return df

        except psycopg2.Error as e:
            print(f"Database error: {e}")
            raise
        except Exception as e:
            print(f"Unexpected error: {e}")
            raise



    @classmethod
    def execute_query(cls, querie, params=None):
        db_string = SupabaseConfig().db_string.get_secret_value()
        with psycopg2.connect(db_string) as conn:
            with conn.cursor() as cur:
                cur.execute(querie, params)
                data = cur.fetchall()
                column_names = [desc[0] for desc in cur.description]  # type: ignore
        return pd.DataFrame(data, columns=column_names)