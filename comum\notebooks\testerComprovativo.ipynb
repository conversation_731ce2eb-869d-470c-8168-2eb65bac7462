{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["Comprovativo(data_criacao=None, data_valor=datetime.datetime(2024, 9, 30, 0, 0), data_movimento=datetime.datetime(2024, 9, 30, 0, 0), movimento='TRF EMPIFARMA PRODUTO', valor=1051.29, tipo_movimento='C', entidade=None, numero_cartao=None, tipo_pagamento=None, id_sibs=None, ordenante='EMPIFARMA PRODUTOS FARMACEUTICOS', conta_destino=None, origem_operacao=None, id_transferencia=None, local=None, swift_destinatario='CGDIPTPL', iban_destinatario='*************************', motivo_sepa=None, observacao=None, ano=2024, mes=9, irs=None, seg_social=None, conta='0507021920600', segmento_id=0, id_chave=None, data_alteracao=None, cont_trf=None, link=None)"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from src.comprovativos.extractorComprovativo import ComprovativoExtractor\n", "\n", "file = '/Users/<USER>/Library/CloudStorage/OneDrive-Pessoal/PROJETOS PYTHON/WEBAPP/src/files/comprovativos/teste.pdf'\n", "extractor = ComprovativoExtractor(file).run_extraction()\n", "extractor"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["APIResponse[TypeVar](data=[{'data_criacao': '2025-02-01T18:48:55.680758+00:00', 'data_valor': '2024-09-30T00:00:00', 'data_movimento': '2024-09-30T00:00:00', 'movimento': 'TRF EMPIFARMA PRODUTO', 'valor': 1051.29, 'tipo_movimento': 'C', 'entidade': 'empifarma', 'numero_cartao': None, 'tipo_pagamento': None, 'id_sibs': None, 'ordenante': 'EMPIFARMA PRODUTOS FARMACEUTICOS', 'conta_destino': None, 'origem_operacao': None, 'id_transferencia': None, 'local': None, 'swift_destinatario': 'CGDIPTPL', 'iban_destinatario': '*************************', 'motivo_sepa': None, 'observacao': None, 'ano': 2024, 'mes': 9, 'irs': None, 'seg_social': None, 'conta': '0507021920600', 'segmento_id': 40, 'id_chave': 'd81ef5c8-57e4-4951-b036-99954a7036f9', 'data_alteracao': '2025-02-01T18:48:55.680758+00:00', 'cont_trf': None, 'link': None}], count=None)"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from src.comprovativos.workerComprovativo import process_uploaded_file\n", "pdf = '/Users/<USER>/Library/CloudStorage/OneDrive-Pessoal/PROJETOS PYTHON/WEBAPP/src/files/comprovativos/teste.pdf'\n", "insert_result = process_uploaded_file(file=pdf, utilizador='ricardo', save=True)\n", "insert_result"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["'d81ef5c8-57e4-4951-b036-99954a7036f9'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["insert_result.data[0]['id_chave']"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["[ComprovativoPadrao(id_chave=35, padrao='%AIRBNB%', segmento_id=32, entidade_id=16, data_criacao=datetime.datetime(2024, 12, 9, 16, 17, 52, 461983, tzinfo=TzInfo(UTC)), data_alteracao=datetime.datetime(2024, 12, 9, 16, 17, 52, 461983, tzinfo=TzInfo(UTC))),\n", " ComprovativoPadrao(id_chave=36, padrao='%COMPRA MBWAY WOO%', segmento_id=13, entidade_id=17, data_criacao=datetime.datetime(2024, 12, 9, 16, 22, 26, 183652, tzinfo=TzInfo(UTC)), data_alteracao=datetime.datetime(2024, 12, 9, 16, 22, 26, 183652, tzinfo=TzInfo(UTC))),\n", " ComprovativoPadrao(id_chave=37, padrao='%MICROSOFT%', segmento_id=10, entidade_id=19, data_criacao=datetime.datetime(2024, 12, 13, 20, 14, 47, 796131, tzinfo=TzInfo(UTC)), data_alteracao=datetime.datetime(2024, 12, 13, 20, 14, 47, 796131, tzinfo=TzInfo(UTC))),\n", " ComprovativoPadrao(id_chave=38, padrao='COMPRAS C.DEB NAVEGAD', segmento_id=34, entidade_id=22, data_criacao=datetime.datetime(2025, 1, 3, 23, 5, 43, 754744, tzinfo=TzInfo(UTC)), data_alteracao=datetime.datetime(2025, 1, 3, 23, 5, 43, 754744, tzinfo=TzInfo(UTC))),\n", " ComprovativoPadrao(id_chave=2, padrao='%MANUTENCAO CONTA%', segmento_id=3, entidade_id=18, data_criacao=datetime.datetime(2024, 12, 5, 15, 8, 42, 167810, tzinfo=TzInfo(UTC)), data_alteracao=datetime.datetime(2025, 1, 7, 22, 29, 8, 181191, tzinfo=TzInfo(UTC))),\n", " ComprovativoPadrao(id_chave=15, padrao='%COMISSAO %', segmento_id=25, entidade_id=18, data_criacao=datetime.datetime(2024, 12, 5, 15, 8, 42, 167810, tzinfo=TzInfo(UTC)), data_alteracao=datetime.datetime(2025, 1, 7, 22, 29, 45, 648142, tzinfo=TzInfo(UTC))),\n", " ComprovativoPadrao(id_chave=1, padrao='%LEVANTAMENTO%', segmento_id=2, entidade_id=18, data_criacao=datetime.datetime(2024, 12, 5, 15, 8, 42, 167810, tzinfo=TzInfo(UTC)), data_alteracao=datetime.datetime(2025, 1, 11, 12, 13, 42, 535068, tzinfo=TzInfo(UTC))),\n", " ComprovativoPadrao(id_chave=8, padrao='TRF CXDAPP', segmento_id=14, entidade_id=18, data_criacao=datetime.datetime(2024, 12, 5, 15, 8, 42, 167810, tzinfo=TzInfo(UTC)), data_alteracao=datetime.datetime(2025, 1, 11, 12, 14, 28, 413985, tzinfo=TzInfo(UTC))),\n", " ComprovativoPadrao(id_chave=9, padrao='%CONTINENTE%', segmento_id=23, entidade_id=2, data_criacao=datetime.datetime(2024, 12, 5, 15, 8, 42, 167810, tzinfo=TzInfo(UTC)), data_alteracao=datetime.datetime(2024, 12, 5, 23, 9, 34, 999487, tzinfo=TzInfo(UTC))),\n", " ComprovativoPadrao(id_chave=10, padrao='%LIDL%', segmento_id=23, entidade_id=1, data_criacao=datetime.datetime(2024, 12, 5, 15, 8, 42, 167810, tzinfo=TzInfo(UTC)), data_alteracao=datetime.datetime(2024, 12, 5, 23, 9, 34, 999487, tzinfo=TzInfo(UTC))),\n", " ComprovativoPadrao(id_chave=11, padrao='%PINGO DOCE%', segmento_id=23, entidade_id=3, data_criacao=datetime.datetime(2024, 12, 5, 15, 8, 42, 167810, tzinfo=TzInfo(UTC)), data_alteracao=datetime.datetime(2024, 12, 5, 23, 9, 34, 999487, tzinfo=TzInfo(UTC))),\n", " ComprovativoPadrao(id_chave=12, padrao='%PAO DE ACUCAR%', segmento_id=23, entidade_id=4, data_criacao=datetime.datetime(2024, 12, 5, 15, 8, 42, 167810, tzinfo=TzInfo(UTC)), data_alteracao=datetime.datetime(2024, 12, 5, 23, 9, 34, 999487, tzinfo=TzInfo(UTC))),\n", " ComprovativoPadrao(id_chave=14, padrao='%MERCADONA%', segmento_id=23, entidade_id=6, data_criacao=datetime.datetime(2024, 12, 5, 15, 8, 42, 167810, tzinfo=TzInfo(UTC)), data_alteracao=datetime.datetime(2024, 12, 5, 23, 9, 34, 999487, tzinfo=TzInfo(UTC))),\n", " ComprovativoPadrao(id_chave=19, padrao='%MCDONALD%', segmento_id=34, entidade_id=7, data_criacao=datetime.datetime(2024, 12, 5, 15, 8, 42, 167810, tzinfo=TzInfo(UTC)), data_alteracao=datetime.datetime(2024, 12, 5, 23, 9, 34, 999487, tzinfo=TzInfo(UTC))),\n", " ComprovativoPadrao(id_chave=20, padrao='%UBER EATS%', segmento_id=34, entidade_id=12, data_criacao=datetime.datetime(2024, 12, 5, 15, 8, 42, 167810, tzinfo=TzInfo(UTC)), data_alteracao=datetime.datetime(2024, 12, 5, 23, 9, 34, 999487, tzinfo=TzInfo(UTC))),\n", " ComprovativoPadrao(id_chave=21, padrao='%ZARA%', segmento_id=35, entidade_id=13, data_criacao=datetime.datetime(2024, 12, 5, 15, 8, 42, 167810, tzinfo=TzInfo(UTC)), data_alteracao=datetime.datetime(2024, 12, 5, 23, 9, 34, 999487, tzinfo=TzInfo(UTC))),\n", " ComprovativoPadrao(id_chave=22, padrao='%EMPIFARMA%', segmento_id=40, entidade_id=8, data_criacao=datetime.datetime(2024, 12, 5, 15, 8, 42, 167810, tzinfo=TzInfo(UTC)), data_alteracao=datetime.datetime(2024, 12, 5, 23, 9, 34, 999487, tzinfo=TzInfo(UTC))),\n", " ComprovativoPadrao(id_chave=13, padrao='%AUCHAN %', segmento_id=23, entidade_id=5, data_criacao=datetime.datetime(2024, 12, 5, 15, 8, 42, 167810, tzinfo=TzInfo(UTC)), data_alteracao=datetime.datetime(2024, 12, 5, 23, 9, 34, 999487, tzinfo=TzInfo(UTC))),\n", " ComprovativoPadrao(id_chave=7, padrao='MEO TMN', segmento_id=13, entidade_id=9, data_criacao=datetime.datetime(2024, 12, 5, 15, 8, 42, 167810, tzinfo=TzInfo(UTC)), data_alteracao=datetime.datetime(2024, 12, 5, 23, 9, 34, 999487, tzinfo=TzInfo(UTC))),\n", " ComprovativoPadrao(id_chave=16, padrao='%IMPOSTO SELO%', segmento_id=26, entidade_id=11, data_criacao=datetime.datetime(2024, 12, 5, 15, 8, 42, 167810, tzinfo=TzInfo(UTC)), data_alteracao=datetime.datetime(2024, 12, 5, 23, 9, 34, 999487, tzinfo=TzInfo(UTC))),\n", " ComprovativoPadrao(id_chave=4, padrao='%TRF P2P 9%', segmento_id=4, entidade_id=10, data_criacao=datetime.datetime(2024, 12, 5, 15, 8, 42, 167810, tzinfo=TzInfo(UTC)), data_alteracao=datetime.datetime(2024, 12, 5, 23, 9, 34, 999487, tzinfo=TzInfo(UTC))),\n", " ComprovativoPadrao(id_chave=6, padrao='%BX VALOR 03-TRANSACCO%', segmento_id=7, entidade_id=14, data_criacao=datetime.datetime(2024, 12, 5, 15, 8, 42, 167810, tzinfo=TzInfo(UTC)), data_alteracao=datetime.datetime(2024, 12, 5, 23, 9, 34, 999487, tzinfo=TzInfo(UTC))),\n", " ComprovativoPadrao(id_chave=17, padrao='%IMPOSTO DO SELO%', segmento_id=26, entidade_id=11, data_criacao=datetime.datetime(2024, 12, 5, 15, 8, 42, 167810, tzinfo=TzInfo(UTC)), data_alteracao=datetime.datetime(2024, 12, 5, 23, 9, 34, 999487, tzinfo=TzInfo(UTC))),\n", " ComprovativoPadrao(id_chave=5, padrao='TRF MBWAY%', segmento_id=4, entidade_id=10, data_criacao=datetime.datetime(2024, 12, 5, 15, 8, 42, 167810, tzinfo=TzInfo(UTC)), data_alteracao=datetime.datetime(2024, 12, 5, 23, 9, 34, 999487, tzinfo=TzInfo(UTC))),\n", " ComprovativoPadrao(id_chave=18, padrao='%BUYON LDA%', segmento_id=34, entidade_id=0, data_criacao=datetime.datetime(2024, 12, 5, 15, 8, 42, 167810, tzinfo=TzInfo(UTC)), data_alteracao=datetime.datetime(2024, 12, 5, 23, 9, 34, 999487, tzinfo=TzInfo(UTC))),\n", " ComprovativoPadrao(id_chave=39, padrao='IUC', segmento_id=52, entidade_id=11, data_criacao=datetime.datetime(2025, 2, 2, 22, 57, 37, 789953, tzinfo=TzInfo(UTC)), data_alteracao=datetime.datetime(2025, 2, 2, 22, 58, 45, 537449, tzinfo=TzInfo(UTC))),\n", " ComprovativoPadrao(id_chave=41, padrao='COMPRA INSURAMA', segmento_id=65, entidade_id=23, data_criacao=datetime.datetime(2025, 2, 2, 23, 2, 10, 179554, tzinfo=TzInfo(UTC)), data_alteracao=datetime.datetime(2025, 2, 2, 23, 2, 10, 179554, tzinfo=TzInfo(UTC))),\n", " ComprovativoPadrao(id_chave=23, padrao='%CINEMA%', segmento_id=31, entidade_id=24, data_criacao=datetime.datetime(2024, 12, 6, 1, 43, 14, 664830, tzinfo=TzInfo(UTC)), data_alteracao=datetime.datetime(2025, 2, 2, 23, 2, 47, 969206, tzinfo=TzInfo(UTC))),\n", " ComprovativoPadrao(id_chave=24, padrao='%cinema%', segmento_id=31, entidade_id=24, data_criacao=datetime.datetime(2024, 12, 6, 1, 49, 8, 694284, tzinfo=TzInfo(UTC)), data_alteracao=datetime.datetime(2025, 2, 2, 23, 2, 52, 151931, tzinfo=TzInfo(UTC))),\n", " ComprovativoPadrao(id_chave=42, padrao='COMPRA SPLIIIT', segmento_id=28, entidade_id=25, data_criacao=datetime.datetime(2025, 2, 15, 9, 13, 46, 412937, tzinfo=TzInfo(UTC)), data_alteracao=datetime.datetime(2025, 2, 15, 9, 13, 46, 412937, tzinfo=TzInfo(UTC))),\n", " ComprovativoPadrao(id_chave=43, padrao='COMPRAS C.DEB PRO4MAT', segmento_id=6, entidade_id=30, data_criacao=datetime.datetime(2025, 2, 15, 10, 53, 57, 247012, tzinfo=TzInfo(UTC)), data_alteracao=datetime.datetime(2025, 2, 15, 10, 57, 2, 709907, tzinfo=TzInfo(UTC))),\n", " ComprovativoPadrao(id_chave=3, padrao='%MANUT.CONTA%', segmento_id=3, entidade_id=28, data_criacao=datetime.datetime(2024, 12, 5, 15, 8, 42, 167810, tzinfo=TzInfo(UTC)), data_alteracao=datetime.datetime(2025, 2, 15, 16, 41, 26, 32968, tzinfo=TzInfo(UTC)))]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["from src.services.supabase_client import connect\n", "from src.comprovativos.classComprovativo import Comprovativo\n", "from src.contas.classConta import Conta\n", "from src.eventos.classEvento import Evento\n", "from src.comprovativos.classComprovativo import ComprovativoPadrao\n", "\n", "supabase = connect()\n", "response = supabase.table('comprovativos').select(\"*, contas(*)\").eq(\"id_chave\", '4f7a941b-f7ad-4667-86c8-e41d855a0b6b').single().execute()\n", "\n", "comprovativo = Comprovativo(**response.data)\n", "conta = Conta(**response.data['contas'])\n", "movimento_ultimos_9 = comprovativo.movimento[-9:]\n", "comprovativo.cont_trf = movimento_ultimos_9[:3] + movimento_ultimos_9[-3:]\n", "response = supabase.table('comprova_padroes').select(\"*\").execute()\n", "padroes = [ComprovativoPadrao(**padrao) for padrao in response.data]\n", "\n", "\n", "def get_pendentes():\n", "    response = supabase.table('eventos').select(\"*\").eq(\"estado\", 0).order(\"data_criacao\", desc=False).execute()\n", "    if response.data:\n", "        return [Evento(**evento) for evento in response.data]\n", "    else:\n", "        return []\n", "    \n", "eventos = get_pendentes()\n", "padroes"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<Response [200 OK]>\n"]}], "source": ["from src.comprovativos.workerComprovativo import WorkerComprovativos\n", "\n", "pdf_path = 'src/comprovativos/files/teste_comprovativo.pdf'\n", "comprovativo = WorkerComprovativos().processar_ficheiro(ficheiro=pdf_path, utilizador='ricardo', save=True)\n", "print(comprovativo)\n", "\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["[ComprovativoPadrao(id=1, id_chave=UUID('dc58c141-55a5-4788-baca-c8120a19ffea'), padrao='LEVANTAMENTO', segmento_id=2, entidade_id=18, ativo=True, data_criacao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035), data_alteracao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035)),\n", " ComprovativoPadrao(id=2, id_chave=UUID('2577badc-a9ff-4269-ba2b-a626e0449b39'), padrao='MANUTENCAO CONTA', segmento_id=3, entidade_id=18, ativo=True, data_criacao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035), data_alteracao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035)),\n", " ComprovativoPadrao(id=3, id_chave=UUID('8f9cb6da-c33c-4569-b2b1-40db7f4d3a9b'), padrao='MANUT.CONTA', segmento_id=3, entidade_id=28, ativo=True, data_criacao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035), data_alteracao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035)),\n", " ComprovativoPadrao(id=4, id_chave=UUID('b0b296d3-b6cc-4dce-9a66-ed3cba1f4056'), padrao='TRF P2P 9', segmento_id=4, entidade_id=10, ativo=True, data_criacao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035), data_alteracao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035)),\n", " ComprovativoPadrao(id=5, id_chave=UUID('2dc6765b-bb7f-4a99-907f-382dac8ff3ed'), padrao='TRF MBWAY', segmento_id=4, entidade_id=10, ativo=True, data_criacao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035), data_alteracao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035)),\n", " ComprovativoPadrao(id=6, id_chave=UUID('ebd0378b-0c7d-4b3a-aa36-fc90f15e401e'), padrao='BX VALOR 03-TRANSACCO', segmento_id=7, entidade_id=14, ativo=True, data_criacao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035), data_alteracao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035)),\n", " ComprovativoPadrao(id=7, id_chave=UUID('4c49bda3-9a20-454b-bd0b-d04e49e070be'), padrao='MEO TMN', segmento_id=13, entidade_id=9, ativo=True, data_criacao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035), data_alteracao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035)),\n", " ComprovativoPadrao(id=8, id_chave=UUID('63833ff9-d686-4d7e-a523-708419a0da06'), padrao='TRF CXDAPP', segmento_id=14, entidade_id=18, ativo=True, data_criacao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035), data_alteracao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035)),\n", " ComprovativoPadrao(id=9, id_chave=UUID('6ec55a01-30b4-48eb-bb8d-450912abdcfd'), padrao='CONTINENTE', segmento_id=23, entidade_id=2, ativo=True, data_criacao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035), data_alteracao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035)),\n", " ComprovativoPadrao(id=10, id_chave=UUID('0a77962d-18b6-4ebb-a87e-bfe32603f4e8'), padrao='LIDL', segmento_id=23, entidade_id=1, ativo=True, data_criacao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035), data_alteracao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035)),\n", " ComprovativoPadrao(id=11, id_chave=UUID('f3e0acd5-0624-427e-b751-8604f17f94d5'), padrao='PINGO DOCE', segmento_id=23, entidade_id=3, ativo=True, data_criacao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035), data_alteracao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035)),\n", " ComprovativoPadrao(id=12, id_chave=UUID('71c56068-e69d-4b67-90b5-69a43bc0b8b5'), padrao='PAO DE ACUCAR', segmento_id=23, entidade_id=4, ativo=True, data_criacao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035), data_alteracao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035)),\n", " ComprovativoPadrao(id=13, id_chave=UUID('8acf63eb-f6e0-4ef5-85f2-53ee9c10b96b'), padrao='AUCHAN ', segmento_id=23, entidade_id=5, ativo=True, data_criacao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035), data_alteracao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035)),\n", " ComprovativoPadrao(id=14, id_chave=UUID('b03f44d9-f28d-403c-8a86-a484c0ea0030'), padrao='COMPRA MERCADONA', segmento_id=23, entidade_id=6, ativo=True, data_criacao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035), data_alteracao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035)),\n", " ComprovativoPadrao(id=15, id_chave=UUID('1745dfe8-ee25-4526-953f-d20b0fbfafee'), padrao='COMISSAO', segmento_id=25, entidade_id=18, ativo=True, data_criacao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035), data_alteracao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035)),\n", " ComprovativoPadrao(id=16, id_chave=UUID('25d15e8a-f071-4733-ae96-c5654460d7c8'), padrao='IMPOSTO SELO', segmento_id=26, entidade_id=11, ativo=True, data_criacao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035), data_alteracao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035)),\n", " ComprovativoPadrao(id=17, id_chave=UUID('0a8598a1-e329-40e9-b783-aeb2a2a29c11'), padrao='IMPOSTO DO SELO', segmento_id=26, entidade_id=11, ativo=True, data_criacao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035), data_alteracao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035)),\n", " ComprovativoPadrao(id=19, id_chave=UUID('0376942b-4af7-43bd-af05-4cd714bb5ea3'), padrao='MCDONALD', segmento_id=34, entidade_id=7, ativo=True, data_criacao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035), data_alteracao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035)),\n", " ComprovativoPadrao(id=20, id_chave=UUID('fa5d339b-0d01-47ac-ac20-ca7839b4f707'), padrao='UBER EATS', segmento_id=34, entidade_id=12, ativo=True, data_criacao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035), data_alteracao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035)),\n", " ComprovativoPadrao(id=21, id_chave=UUID('d0f0c691-9026-4e7a-980f-07cb1d5adcd4'), padrao='ZARA', segmento_id=35, entidade_id=13, ativo=True, data_criacao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035), data_alteracao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035)),\n", " ComprovativoPadrao(id=22, id_chave=UUID('3d7b55b3-d238-4f89-9611-8d4b660221a8'), padrao='EMPIFARMA', segmento_id=40, entidade_id=8, ativo=True, data_criacao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035), data_alteracao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035)),\n", " ComprovativoPadrao(id=23, id_chave=UUID('eb3d728e-4aad-4e6d-a8c9-c89d341b5ebd'), padrao='CINEMA', segmento_id=31, entidade_id=24, ativo=True, data_criacao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035), data_alteracao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035)),\n", " ComprovativoPadrao(id=35, id_chave=UUID('ecf3b3b4-ef79-4867-9cf4-080bb8991c9f'), padrao='AIRBNB', segmento_id=32, entidade_id=16, ativo=True, data_criacao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035), data_alteracao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035)),\n", " ComprovativoPadrao(id=36, id_chave=UUID('b6a7c858-2bd4-4eca-843d-00acf17dd787'), padrao='COMPRA MBWAY WOO', segmento_id=13, entidade_id=17, ativo=True, data_criacao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035), data_alteracao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035)),\n", " ComprovativoPadrao(id=37, id_chave=UUID('13580f93-0c9d-4106-911c-391fd9c20b02'), padrao='MICROSOFT', segmento_id=10, entidade_id=19, ativo=True, data_criacao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035), data_alteracao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035)),\n", " ComprovativoPadrao(id=38, id_chave=UUID('8b2e1f14-c43b-4578-82f1-82097bcb295e'), padrao='COMPRAS C.DEB NAVEGAD', segmento_id=34, entidade_id=22, ativo=True, data_criacao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035), data_alteracao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035)),\n", " ComprovativoPadrao(id=39, id_chave=UUID('a85d8139-1c69-4e9a-8caa-aa8d5b18fabe'), padrao='IUC', segmento_id=52, entidade_id=11, ativo=True, data_criacao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035), data_alteracao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035)),\n", " ComprovativoPadrao(id=41, id_chave=UUID('391ed4f7-578c-4ca2-8b3a-6cbec9ea53e8'), padrao='COMPRA INSURAMA', segmento_id=65, entidade_id=23, ativo=True, data_criacao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035), data_alteracao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035)),\n", " ComprovativoPadrao(id=42, id_chave=UUID('15a6f356-4318-4a34-b759-13b14e62f7e6'), padrao='COMPRA SPLIIIT', segmento_id=28, entidade_id=25, ativo=True, data_criacao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035), data_alteracao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035)),\n", " ComprovativoPadrao(id=43, id_chave=UUID('b52db9fb-d114-4143-bbc7-e130cb7579a8'), padrao='COMPRAS C.DEB PRO4MAT', segmento_id=6, entidade_id=30, ativo=True, data_criacao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035), data_alteracao=datetime.datetime(2025, 5, 4, 16, 21, 22, 357035))]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from src.services.api import api_get\n", "from src.comprovativos.classComprovativo import ComprovativoPadrao\n", "\n", "response = api_get(tabela=\"comprova_padroes\")\n", "padroes = [ComprovativoPadrao(**padrao) for padrao in response.json()]\n", "padroes\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.0"}}, "nbformat": 4, "nbformat_minor": 2}