from typing import List
from sqlmodel import Session
from fastapi import APIRouter, Depends
from comum.models.contactos_telefones import ContactoTelefoneBase, ContactoTelefoneCreate, ContactoTelefoneUpdate
from app.database.postgres import get_session
from app.workers.contactos_telefones import ContactosTelefonesWorker


contactos_telefones_router = APIRouter()

def get_worker(session: Session = Depends(get_session)) -> ContactosTelefonesWorker:
    return ContactosTelefonesWorker(session)


@contactos_telefones_router.get("/contactos_telefones", response_model=List[ContactoTelefoneBase])
async def lista_completa(worker: ContactosTelefonesWorker = Depends(get_worker)):
    return list(worker.lista())


@contactos_telefones_router.get("/contactos_telefones/{id}", response_model=ContactoTelefoneBase)
async def procura(id: int, worker: ContactosTelefonesWorker = Depends(get_worker)):
    return worker.procura(id)


@contactos_telefones_router.post("/contactos_telefones", response_model=ContactoTelefoneBase)
async def cria(novos_dados: ContactoTelefoneCreate, worker: ContactosTelefonesWorker = Depends(get_worker)):
    return worker.cria(novos_dados)


@contactos_telefones_router.patch("/contactos_telefones/{id}", response_model=ContactoTelefoneBase)
async def atualiza(id: int, dados: ContactoTelefoneUpdate, worker: ContactosTelefonesWorker = Depends(get_worker)):
    return worker.atualiza(id, dados)
    

@contactos_telefones_router.delete("/contactos_telefones/{id}")
async def remove(id: int, worker: ContactosTelefonesWorker = Depends(get_worker)):
    worker.remove(id)
    return {"message": f"ID: **{id}** deleted!"}