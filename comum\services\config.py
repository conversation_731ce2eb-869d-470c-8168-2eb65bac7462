import os
from dotenv import load_dotenv

load_dotenv() 



DATABASE_URL = os.getenv("DATABASE_URL")
DATABASE_URL_LOCAL = os.getenv("DATABASE_URL_LOCAL")


PGSQLAPI_PORT = int(os.getenv("PGSQLAPI_PORT", 0))
PGSQLAPI_URL = os.getenv("PGSQLAPI_URL")


PAPERLESS_API_PORT = int(os.getenv("PAPERLESS_API_PORT", 0))
PAPERLESS_API_URL = os.getenv("PAPERLESS_API_URL", "")
PAPERLESS_API_KEY = os.getenv("PAPERLESS_API_KEY", "")

SUPABASE_URL = os.getenv("SUPABASE_URL", "")
SUPABASE_KEY = os.getenv("SUPABASE_KEY", "")
SUPABASE_HOST = os.getenv("SUPABASE_HOST", "")
SUPABASE_DATABASE = os.getenv("SUPABASE_DATABASE", "")
SUPABASE_PORT = int(os.getenv("SUPABASE_PORT", 0))
SUPABASE_USER = os.getenv("SUPABASE_USER", "")
SUPABASE_PASSWORD = os.getenv("SUPABASE_PASSWORD", "")
DB_STRING = os.getenv("DB_STRING", "")

ICLOUD_USER = os.getenv("ICLOUD_USER", "")
ICLOUD_PASS = os.getenv("ICLOUD_PASS", "")





