from typing import List
from sqlmodel import Session
from fastapi import APIRouter, Depends
from comum.models.eventos import EventoBase, EventoUpdate
from app.database.postgres import get_session
from app.workers.eventos import EventosWorker


eventos_router = APIRouter()

def get_worker(session: Session = Depends(get_session)) -> EventosWorker:
    return EventosWorker(session)


@eventos_router.get("/eventos", response_model=List[EventoBase])
async def lista_completa(worker: EventosWorker = Depends(get_worker), estado: int = None):
    return list(worker.lista(estado))

@eventos_router.get("/eventos/pendentes", response_model=List[EventoBase])
async def lista_pendentes(worker: EventosWorker = Depends(get_worker)):
    return list(worker.lista_pendentes())

@eventos_router.get("/eventos/{id}", response_model=EventoBase)
async def procura(id: int, worker: EventosWorker = Depends(get_worker)):
    return worker.procura(id)


@eventos_router.patch("/eventos/{id}", response_model=EventoBase)
async def atualiza(id: int, dados: EventoUpdate, worker: EventosWorker = Depends(get_worker)):
    return worker.atualiza(id, dados)
    

@eventos_router.delete("/eventos/{id}")
async def remove(id: int, worker: EventosWorker = Depends(get_worker)):
    worker.remove(id)
    return {"message": f"ID: **{id}** deleted!"}