import streamlit as st
from src.querys.classQuery import Query
import psycopg2
import pandas as pd
from src.services.supabase_client import SupabaseConfig, connect
from src.entidades.classEntidade import Entidade


supabase = connect()


def execute_query(querie, params=None):
    db_string = SupabaseConfig().db_string.get_secret_value()
    with psycopg2.connect(db_string) as conn:
        with conn.cursor() as cur:
            cur.execute(querie, params)
            data = cur.fetchall()
            column_names = [desc[0] for desc in cur.description]  # type: ignore
    return pd.DataFrame(data, columns=column_names)







entidades = Entidade.get_entidades()
entidades_list = [entidade.entidade for entidade in entidades]




def main():
    st.title("QUERIES")
    querys = Query.all_querys()
    for query in querys:
        # if query.id_chave == 8:
        #     st.write(f"{query.id_chave} - {query.descricao}")
        #     entidade = ['lidl']
        #     ano = [2025]
        #     params = {'entidade': entidade, 'ano': ano}
        #     dados = execute_query(query.query, params)
        #     st.write(dados)

        with st.expander(f"{query.id_chave} - {query.descricao}"):
            if query.id_chave == 8:

                entidade_selected = st.selectbox("Selecionar entidade:", entidades_list, index=1, key=f"entidade_{query.id_chave}")
                ano_selected = st.selectbox("Selecionar ano:", [2024, 2025], index=0, key=f"ano_{query.id_chave}")
                params = {'entidade': [entidade_selected], 'ano': [ano_selected]}
                if st.button("teste", key=f"execute_{query.id_chave}"):
                    dados = execute_query(query.query, params)
                    st.dataframe(dados)

            st.code(query.query, 'sql', line_numbers=True, wrap_lines=True)

if __name__ == "__main__":
    if not st.session_state.get('authenticated'):
        switch_page("main")
    else:
        main()