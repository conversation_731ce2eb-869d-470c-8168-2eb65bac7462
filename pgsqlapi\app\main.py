from fastapi import FastAPI
from contextlib import asynccontextmanager
from app.database.postgres import database_init
from app.routes.estados import estados_router
from app.routes.eventos import eventos_router
from app.routes.segmentos import segmentos_router
from app.routes.entidades import entidades_router
from app.routes.contas import contas_router
from app.routes.ficheiros import ficheiros_router
from app.routes.recibos import recibos_router
from app.routes.recibos_codigos import recibos_codigos_router
from app.routes.contactos import contactos_router
from app.routes.contactos_telefones import contactos_telefones_router
from app.routes.faturas_cabec import faturas_cabec
from app.routes.faturas_linhas import faturas_linhas
from app.routes.comprovativos import comprovativos_router
from app.routes.comprova_padroes import comprova_padroes_router

@asynccontextmanager
async def lifespan(app: FastAPI):
    database_init()
    yield




app = FastAPI(lifespan=lifespan, swagger_ui_parameters={"syntaxHighlight": {"theme": "nord"}, "tryItOutEnabled": True, "displayRequestDuration": True, "deepLinking": True, "defaultModelsExpandDepth": -1, "docExpansion": "none"})
app.include_router(estados_router,prefix="/api/v1",tags=["Estados"])
app.include_router(eventos_router,prefix="/api/v1",tags=["Eventos"])
app.include_router(segmentos_router,prefix="/api/v1",tags=["Segmentos"])
app.include_router(entidades_router,prefix="/api/v1",tags=["Entidades"])
app.include_router(contas_router,prefix="/api/v1",tags=["Contas"])
app.include_router(ficheiros_router,prefix="/api/v1",tags=["Ficheiros"])
app.include_router(recibos_router,prefix="/api/v1",tags=["Recibos"])
app.include_router(recibos_codigos_router,prefix="/api/v1",tags=["Recibos Codigos"])
app.include_router(contactos_router,prefix="/api/v1",tags=["Contactos"])
app.include_router(contactos_telefones_router,prefix="/api/v1",tags=["Contactos Telefones"])
app.include_router(faturas_cabec,prefix="/api/v1",tags=["Faturas Cabec"])
app.include_router(faturas_linhas,prefix="/api/v1",tags=["Faturas Linhas"])
app.include_router(comprovativos_router,prefix="/api/v1",tags=["Comprovativos"])
app.include_router(comprova_padroes_router,prefix="/api/v1",tags=["Comprova Padroes"])



