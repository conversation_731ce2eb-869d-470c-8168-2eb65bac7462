import schedule
import time
from datetime import datetime
from comum.services.logger import setup_logger
from app.services.api_service import ApiService
from app.services.document_service import DocumentService
from app.services.eventos_service import EventosService

api_service = ApiService()
document_service = DocumentService(api_service)
eventos_service = EventosService(api_service)
logger = setup_logger()

def processar_documentos():
    try:
        nao_processados_ngx = api_service.get_unprocessed_documents()
        if nao_processados_ngx.count > 0:
            logger.info(msg=f"{nao_processados_ngx.count} Documentos não processados detectados: {nao_processados_ngx.all}", ThreadName="DocumentosWatcher", Application="pgsqlops")
            for documento in nao_processados_ngx.results:
                document_service.process_document(documento)
                
    except Exception as e:
        logger.critical(msg=f"Erro processar_documentos(): {type(e).__name__} - {str(e)}", ThreadName="DocumentosWatcher", Application="pgsqlops", exc_info=True)


def processar_eventos_pendentes():
    try:
        eventos_pendentes = api_service.eventos_pendentes()
        if eventos_pendentes:
            logger.info(msg=f"{len(eventos_pendentes)} Eventos pendentes detectados.", ThreadName="EventosWatcher", Application="pgsqlops")
            for evento in eventos_pendentes:
                eventos_service.processar_evento(evento)
    except Exception as e:
        logger.critical(msg=f"Erro processar_eventos(): {type(e).__name__} - {str(e)}", ThreadName="EventosWatcher", Application="pgsqlops", exc_info=True)





def ligar_operador(with_schedule: bool = True):
    agora = datetime.now().strftime("%d-%m-%Y %H:%M:%S")
    print(f'{agora} - 🟢 ON')
    logger.info(msg="🟢 ON", ThreadName="Operador", Application="pgsqlops")
    processar_documentos()
    processar_eventos_pendentes()

    if with_schedule:
        schedule.every(90).seconds.do(processar_documentos)
        schedule.every(60).seconds.do(processar_eventos_pendentes)
        try:
            while True:
                try:
                    schedule.run_pending()
                except Exception as e:
                    logger.error(msg=f"Erro de schedule: {type(e).__name__} - {str(e)}", ThreadName="Operador", Application="pgsqlops", exc_info=True)
                time.sleep(1)
        except KeyboardInterrupt:
            agora = datetime.now().strftime("%d-%m-%Y %H:%M:%S")
            print(f'{agora} - 🔴 OFF MANUAL')
            logger.critical(msg="🔴 OFF MANUAL", ThreadName="Operador", Application="pgsqlops")

if __name__ == "__main__":
    ligar_operador()


