from typing import List
from sqlmodel import Session
from fastapi import APIRouter, Depends
from comum.models.faturas_linhas import FaturaLinhasBase, FaturaLinhasCreate, FaturaLinhasUpdate
from app.database.postgres import get_session
from app.workers.faturas_linhas import FaturasLinhasWorker


faturas_linhas = APIRouter()

def get_worker(session: Session = Depends(get_session)) -> FaturasLinhasWorker:
    return FaturasLinhasWorker(session)


@faturas_linhas.get("/faturas_linhas", response_model=List[FaturaLinhasBase])
async def lista_completa(worker: FaturasLinhasWorker = Depends(get_worker)):
    return list(worker.lista())


@faturas_linhas.get("/faturas_linhas/{id}", response_model=FaturaLinhasBase)
async def procura(id: int, worker: FaturasLinhasWorker = Depends(get_worker)):
    return worker.procura(id)


@faturas_linhas.post("/faturas_linhas", response_model=FaturaLinhasBase)
async def cria(novos_dados: Fatura<PERSON>inhas<PERSON><PERSON>, worker: FaturasLinhasWorker = Depends(get_worker)):
    return worker.cria(novos_dados)


@faturas_linhas.patch("/faturas_linhas/{id}", response_model=FaturaLinhasBase)
async def atualiza(id: int, dados: FaturaLinhasUpdate, worker: FaturasLinhasWorker = Depends(get_worker)):
    return worker.atualiza(id, dados)
    

@faturas_linhas.delete("/faturas_linhas/{id}")
async def remove(id: int, worker: FaturasLinhasWorker = Depends(get_worker)):
    worker.remove(id)
    return {"message": f"ID: **{id}** deleted!"}