{"cells": [{"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["import sqlite3\n", "import json\n", "from datetime import datetime\n", "from src.services.supabase_client import connect\n", "from src.comprovativos.classComprovativo import Comprovativo, ComprovativoPadrao\n", "from src.contactos.classContacto import ContactoClean\n", "\n", "DB_PATH = r'C:\\casa-server\\src\\_backup\\supabase_backup.db'\n", "supabase = connect()\n", "\n", "    \n", "conn = sqlite3.connect(DB_PATH)\n", "cursor = conn.cursor()\n", "\n", "\n", "def to_isoformat(value):\n", "    if isinstance(value, datetime):\n", "        return value.isoformat()\n", "    return value\n", "\n", "def to_json_string(value):\n", "    return json.dumps(value) if value else None\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# COMPROVA_PADROES"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["response = supabase.from_('comprova_padroes').select('*').execute()\n", "comprovativos_padrao = [ComprovativoPadrao(**comprovativo) for comprovativo in response.data]\n", "cursor.execute(\"DELETE FROM comprova_padroes;\")\n", "\n", "insert_query = \"\"\"\n", "INSERT INTO comprova_padroes (\n", "     padrao\n", "    ,segmento_id\n", "    ,entidade_id\n", "    ,data_criacao\n", "    ,data_alteracao\n", "    ) \n", "VALUES (?, ?, ?, ?, ?);\n", "\"\"\"\n", "\n", "\n", "for comprovativo in comprovativos_padrao:\n", "    cursor.execute(insert_query, (\n", "        comprovativo.padrao, \n", "        comprovativo.segmento_id, \n", "        comprovativo.entidade_id,\n", "        to_isoformat(comprovativo.data_criacao),\n", "        to_isoformat(comprovativo.data_alteracao)\n", "    ))\n", "\n", "\n", "conn.commit()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# COMPROVATIVOS"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["response = supabase.from_('comprovativos').select('*').execute()\n", "comprovativos = [Comprovativo(**comprovativo) for comprovativo in response.data]\n", "cursor.execute(\"DELETE FROM comprovativos;\")\n", "insert_query = \"\"\"\n", "INSERT INTO comprovativos (\n", "     id_chave\n", "    ,data_criacao\n", "    ,data_valor\n", "    ,data_movimento\n", "    ,movimento\n", "    ,valor\n", "    ,tipo_movimento\n", "    ,entidade\n", "    ,numero_cartao\n", "    ,tipo_pagamento\n", "    ,id_sibs, ordenante\n", "    ,conta_destino\n", "    ,origem_operacao\n", "    ,id_transferencia\n", "    ,local\n", "    ,swift_destinatario\n", "    ,iban_destinatario\n", "    ,motivo_sepa\n", "    ,observacao\n", "    ,ano\n", "    ,mes\n", "    ,irs\n", "    ,seg_social\n", "    ,conta\n", "    ,segmento_id\n", "    ,data_alteracao\n", "    ,cont_trf\n", "    ,link\n", "    ,entidade_id\n", ") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);\n", "\"\"\"\n", "\n", "\n", "for comprovativo in comprovativos:\n", "    cursor.execute(insert_query, (\n", "        str(comprovativo.id_chave), \n", "        to_isoformat(comprovativo.data_criacao), \n", "        to_isoformat(comprovativo.data_valor),\n", "        to_isoformat(comprovativo.data_movimento), \n", "        comprovativo.movimento, \n", "        comprovativo.valor, \n", "        comprovativo.tipo_movimento,\n", "        comprovativo.entidade, \n", "        comprovativo.numero_cartao, \n", "        comprovativo.tipo_pagamento, \n", "        comprovativo.id_sibs,\n", "        comprovativo.ordenante, \n", "        comprovativo.conta_destino, \n", "        comprovativo.origem_operacao, \n", "        comprovativo.id_transferencia,\n", "        comprovativo.local, \n", "        comprovativo.swift_destinatario, \n", "        comprovativo.iban_destinatario, \n", "        comprovativo.motivo_sepa,\n", "        comprovativo.observacao, \n", "        comprovativo.ano, \n", "        comprovativo.mes, \n", "        comprovativo.irs, \n", "        comprovativo.seg_social,\n", "        comprovativo.conta, \n", "        comprovativo.segmento_id, \n", "        to_isoformat(comprovativo.data_alteracao), \n", "        comprovativo.cont_trf,\n", "        comprovativo.link, \n", "        comprovativo.entidade_id\n", "    ))\n", "conn.commit()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# CONTACTOS"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["response = supabase.from_('contactos').select('*').execute()\n", "contactos = [ContactoClean(**contacto) for contacto in response.data]\n", "\n", "cursor.execute(\"DELETE FROM contactos;\")\n", "\n", "# Query para inserir dados\n", "insert_query = \"\"\"\n", "INSERT INTO contactos (\n", "     id_chave\n", "    ,nome\n", "    ,apelido\n", "    ,telefones\n", "    ,mails\n", "    ,entidade\n", "    ,origem\n", "    ,data_criacao\n", "    ,data_alteracao\n", ") VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?);\n", "\"\"\"\n", "\n", "for contacto in contactos:\n", "    cursor.execute(insert_query, (\n", "        contacto.id_chave, \n", "        contacto.nome, \n", "        contacto.apelido,\n", "        str(contacto.telefones), \n", "        str(contacto.mails),\n", "        contacto.entidade, \n", "        contacto.origem,\n", "        to_isoformat(contacto.data_criacao), \n", "        to_isoformat(contacto.data_alteracao)\n", "    ))\n", "\n", "conn.commit()\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## CONTACTOS_TELEFONES"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["from src.contactos.classContacto import ContactosTelefones\n", "\n", "response = supabase.from_('contactos_telefones').select('*').execute()\n", "contactos = [ContactosTelefones(**contacto) for contacto in (response.data or [])]\n", "\n", "step1 = \"DROP TABLE IF EXISTS contactos_telefones;\"\n", "step2 = \"\"\"\n", "            CREATE TABLE contactos_telefones (\n", "                id_chave SERIAL PRIMARY KEY,\n", "                contacto_id TEXT,\n", "                telefone TEXT,\n", "                tipo TEXT,\n", "                cont_trf TEXT,\n", "                data_criacao TIMESTAMP,\n", "                data_alteracao TIMESTAMP\n", ");\n", "\"\"\"\n", "\n", "cursor.execute(step1)\n", "cursor.execute(step2)\n", "\n", "step3 = \"\"\"\n", "        INSERT INTO contactos_telefones (\n", "            id_chave,\n", "            contacto_id,\n", "            telefone,\n", "            tipo,\n", "            cont_trf,\n", "            data_criacao,\n", "            data_alteracao\n", "        ) VALUES (?, ?, ?, ?, ?, ?, ?);\n", "\"\"\"\n", "\n", "for contacto in contactos:\n", "    cursor.execute(step3, (\n", "                            contacto.id_chave, \n", "                            contacto.contacto_id, \n", "                            contacto.telefone,\n", "                            contacto.tipo, \n", "                            contacto.cont_trf,\n", "                            to_isoformat(contacto.data_criacao), \n", "                            to_isoformat(contacto.data_alteracao)\n", "                        ))\n", "\n", "conn.commit()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# CONTAS"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["from src.contas.classConta import Conta\n", "\n", "response = supabase.from_('contas').select('*').execute()\n", "contas = [Conta(**conta) for conta in (response.data or [])]\n", "\n", "step1 = \"DROP TABLE IF EXISTS contas;\"\n", "step2 = \"\"\"\n", "            CREATE TABLE contas (\n", "                id_chave SERIAL PRIMARY KEY,\n", "                conta TEXT,\n", "                utilizador TEXT,\n", "                data_criacao TIMESTAMP,\n", "                data_alteracao TIMESTAMP\n", ");\n", "\"\"\"\n", "\n", "cursor.execute(step1)\n", "cursor.execute(step2)\n", "\n", "step3 = \"\"\"\n", "        INSERT INTO contas (\n", "            id_chave,\n", "            conta,\n", "            util<PERSON><PERSON>,\n", "            data_criacao,\n", "            data_alteracao\n", "        ) VALUES (?, ?, ?, ?, ?);\n", "\"\"\"\n", "\n", "for conta in contas:\n", "    cursor.execute(step3, (\n", "                            conta.id_chave, \n", "                            conta.conta, \n", "                            conta.utilizador,\n", "                            to_isoformat(conta.data_criacao), \n", "                            to_isoformat(conta.data_alteracao)\n", "                        ))\n", "\n", "conn.commit()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# ENTIDADES"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["from src.entidades.classEntidade import Entidade\n", "\n", "response = supabase.from_('entidades').select('*').execute()\n", "entidades = [Entidade(**entidade) for entidade in (response.data or [])]\n", "\n", "step1 = \"DROP TABLE IF EXISTS entidades;\"\n", "step2 = \"\"\"\n", "            CREATE TABLE entidades (\n", "                id_chave SERIAL PRIMARY KEY,\n", "                entidade TEXT,\n", "                data_criacao TIMESTAMP,\n", "                data_alteracao TIMESTAMP\n", ");\n", "\"\"\"\n", "\n", "cursor.execute(step1)\n", "cursor.execute(step2)\n", "\n", "step3 = \"\"\"\n", "        INSERT INTO entidades (\n", "            id_chave,\n", "            entidade,\n", "            data_criacao,\n", "            data_alteracao\n", "        ) VALUES (?, ?, ?, ?);\n", "\"\"\"\n", "\n", "for entidade in entidades:\n", "    cursor.execute(step3, (\n", "                            entidade.id_chave, \n", "                            entidade.entidade, \n", "                            to_isoformat(entidade.data_criacao), \n", "                            to_isoformat(entidade.data_alteracao)\n", "                        ))\n", "\n", "conn.commit()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# ESTADOS"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["from src.eventos.classEvento import Estado\n", "\n", "response = supabase.from_('estados').select('*').execute()\n", "estados = [Estado(**estado) for estado in (response.data or [])]\n", "\n", "step1 = \"DROP TABLE IF EXISTS estados;\"\n", "step2 = \"\"\"\n", "            CREATE TABLE estados (\n", "                id_chave SERIAL PRIMARY KEY,\n", "                estado SERIAL,\n", "                detalhe TEXT,\n", "                cor TEXT,\n", "                data_criacao TIMESTAMP,\n", "                data_alteracao TIMESTAMP\n", ");\n", "\"\"\"\n", "\n", "cursor.execute(step1)\n", "cursor.execute(step2)\n", "\n", "step3 = \"\"\"\n", "        INSERT INTO estados (\n", "            id_chave,\n", "            estado,\n", "            de<PERSON><PERSON>,\n", "            cor,\n", "            data_criacao,\n", "            data_alteracao\n", "        ) VALUES (?, ?, ?, ?, ?, ?);\n", "\"\"\"\n", "\n", "for estado in estados:\n", "    cursor.execute(step3, (\n", "                            str(estado.id_chave), \n", "                            estado.estado, \n", "                            estado.de<PERSON><PERSON>,\n", "                            estado.cor,\n", "                            to_isoformat(estado.data_criacao), \n", "                            to_isoformat(estado.data_alteracao)\n", "                        ))\n", "\n", "conn.commit()"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["# conn.close()\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.6"}}, "nbformat": 4, "nbformat_minor": 2}