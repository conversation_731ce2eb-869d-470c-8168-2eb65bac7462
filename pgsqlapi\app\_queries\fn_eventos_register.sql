CREATE OR REPLACE FUNCTION eventos_register()
R<PERSON><PERSON>NS TRIGGER AS $$
BEGIN
    SET search_path TO public;   
    INSERT INTO eventos (tipo, tabela, fkid_chave)
    VALUES (
                TG_OP, 
                TG_TABLE_NAME,                 
                CASE
                    WHEN TG_OP = 'DELETE' THEN OLD.id_chave
                    ELSE NEW.id_chave
                END
            );
    
    IF TG_OP = 'DELETE' THEN
        RETURN OLD;
    ELSE
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql;