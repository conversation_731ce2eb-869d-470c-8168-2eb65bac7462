{"cells": [{"cell_type": "markdown", "id": "95f40045", "metadata": {}, "source": ["## get pendentes - extrai texto - insere no postgres - atualiza no paperless"]}, {"cell_type": "code", "execution_count": 135, "id": "51a39589", "metadata": {}, "outputs": [], "source": ["import httpx\n", "from comum.services.config import PAPERLESS_API_URL, PAPERLESS_API_KEY\n", "from pgsqlops.app.services.api_service import ApiService\n", "from comum.models.paperless import PaperlessModel\n", "api_service = ApiService()\n", "\n", "\n", "nao_processados = api_service.get_unprocessed_documents()\n", "\n", "\n", "\n", "def atualiza_documento(documento_id: int, payload: dict):\n", "    url = PAPERLESS_API_URL + f\"documents/{documento_id}/\"\n", "    headers = {'Authorization': f'Token {PAPERLESS_API_KEY}'}\n", "    response = httpx.patch(url, json=payload, headers=headers)\n", "    return response.json()\n", "\n", "\n", "# for documento in get_nao_processados()['results']:\n", "#     if documento['document_type'] == 1:\n", "#         from comum.models.comprovativos import ComprovativoNGX, ComprovativoCreate\n", "#         comprovativo = ComprovativoExtractor(documento['content'], documento['original_file_name']).run_extraction()\n", "#         comprovativo = ComprovativoCreate(**comprovativo)\n", "#         url = \"http://127.0.0.1:5002/api/v1/comprovativos\"\n", "#         response = httpx.post(url, json=jsonable_encoder(comprovativo.model_dump(exclude_none=True)))\n", "#         id_chave = response.json().get(\"id_chave\")\n", "\n", "#         payload = {\n", "#                     \"title\": str(id_chave),\n", "#                     \"custom_fields\": [\n", "#                         {\n", "#                             \"field\": 3,             # ← ID do campo \"processado\"\n", "#                             \"value\": True\n", "#                         }\n", "#                     ]\n", "#                 }\n", "#         atualiza_documento(documento['id'], payload)\n", "#         print(response)\n", "\n", "# if nao_processados['count'] == 0:\n", "#     print(\"Não existem documentos não processados\")\n", "# else:\n", "#     print(\"documentos detetados\")\n", "    # teste = nao_processados['results'][0]\n", "    # id_teste = teste['id']\n", "    # tags = teste['tags']\n", "    # tags.remove(5) # remove uma tag do documento pelo id\n", "    # url = PAPERLESS_API_URL + f\"documents/{id_teste}/\"\n", "    # headers = {'Authorization': f'Token {PAPERLESS_API_KEY}'}\n", "    # payload = {\n", "    #     \"tags\": tags,\n", "    #     \"custom_fields\": [\n", "    #         {\n", "    #             \"field\": 3,  # ID do campo \"processado\"\n", "    #             \"value\": True\n", "    #         }\n", "    #     ]\n", "    # }\n", "\n", "    # response = atualiza_documento(id_teste, payload)\n", "    # print(response)"]}, {"cell_type": "code", "execution_count": 136, "id": "cf28de50", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'count': 3,\n", " 'next': None,\n", " 'previous': None,\n", " 'all': [60, 58, 59],\n", " 'results': [{'id': 60,\n", "   'correspondent': None,\n", "   'document_type': 2,\n", "   'storage_path': 2,\n", "   'title': '*********_RC_2025_M04P1_F00037',\n", "   'content': 'SMIR LDA RECIBO DE REMUNERAÇÕES\\n\\nRua da Igreja nº 26 00037 Neide Dalila <PERSON>\\nRua da Fonte nº151 Casal dos Silvas\\nCarapinheira\\nMontemor o Velho\\n3140-077 Carapinheira 3140-298\\nNIF: ********* 30-04-2025 - Abril Escriturária 3ª N.I.F. : *********\\nPT50001800035371093502071 Seg. Social: 11917004870\\nFidelidade Mundial SA AT65678959 Salário Base: € 955,00\\nABONOS QUANTIDADE VALOR UNITÁRIO VALOR\\nA07 Subsidio Natal 2,50D 31,832 79,58\\nA08 Subsidio Ferias 2,50D 31,832 79,58\\nA61 S.Alimentaçao Cartão 20,00 9,60 192,00\\nA62 Desc.Cartão Refeição 20,00 -9,60 -192,00\\nA68 Vencimento Base 30,00D 31,8333 955,00\\nDESCONTOS VAL./TAXA INCIDÊNCIA VALOR\\nD01 Segurança Social 11,00% 1.114,16 122,56\\nD02 Imp. s/Rend. Base 3,98% 955,00 38,00\\nD02 Imp. s/Rend. Base 3,77% 159,16 6,00\\n\\n\\n\\n\\nTotal Ilíquido: € 1.114,16\\nNão Cas.,0 Dep.\\nTotal de Descontos: € 166,56\\nAc.Incid.IRS Ac.Ret.IRS Ac.S.Tx.IRS\\nAssinatura € 1.114,16 € 44,00 € 0,00 Líquido a receber: € 947,60\\nFilosoft SIGEP.32 (20.1.0.12) - [H919A2503] - SMIR, LDA (c) FiloSoft - Software, Lda.\\n\\n\\n\\n\\nSMIR LDA RECIBO DE REMUNERAÇÕES\\n\\nRua da Igreja nº 26 00037 Neide Dalila Azevedo Aguiar\\nRua da Fonte nº151 Casal dos Silvas\\nCarapinheira\\nMontemor o Velho\\n3140-077 Carapinheira 3140-298\\nNIF: ********* 30-04-2025 - Abril Escriturária 3ª N.I.F. : *********\\nPT50001800035371093502071 Seg. Social: 11917004870\\nFidelidade Mundial SA AT65678959 Salário Base: € 955,00\\nABONOS QUANTIDADE VALOR UNITÁRIO VALOR\\nA07 Subsidio Natal 2,50D 31,832 79,58\\nA08 Subsidio Ferias 2,50D 31,832 79,58\\nA61 S.Alimentaçao Cartão 20,00 9,60 192,00\\nA62 Desc.Cartão Refeição 20,00 -9,60 -192,00\\nA68 Vencimento Base 30,00D 31,8333 955,00\\nDESCONTOS VAL./TAXA INCIDÊNCIA VALOR\\nD01 Segurança Social 11,00% 1.114,16 122,56\\nD02 Imp. s/Rend. Base 3,98% 955,00 38,00\\nD02 Imp. s/Rend. Base 3,77% 159,16 6,00\\n\\n\\n\\n\\nTotal Ilíquido: € 1.114,16\\nNão Cas.,0 Dep.\\nTotal de Descontos: € 166,56\\nAc.Incid.IRS Ac.Ret.IRS Ac.S.Tx.IRS\\nAssinatura € 1.114,16 € 44,00 € 0,00 Líquido a receber: € 947,60\\nFilosoft SIGEP.32 (20.1.0.12) - [H919A2503] - SMIR, LDA (c) FiloSoft - Software, Lda.',\n", "   'tags': [8],\n", "   'created': '2025-04-30T00:00:00+01:00',\n", "   'created_date': '2025-04-30',\n", "   'modified': '2025-05-17T00:16:14.603470+01:00',\n", "   'added': '2025-05-17T00:16:12.313533+01:00',\n", "   'deleted_at': None,\n", "   'archive_serial_number': None,\n", "   'original_file_name': '*********_RC_2025_M04P1_F00037.pdf',\n", "   'archived_file_name': '2025-04-30 *********_RC_2025_M04P1_F00037.pdf',\n", "   'owner': 3,\n", "   'user_can_change': True,\n", "   'is_shared_by_requester': True,\n", "   'notes': [],\n", "   'custom_fields': [{'value': False, 'field': 3}],\n", "   'page_count': 1,\n", "   'mime_type': 'application/pdf'},\n", "  {'id': 58,\n", "   'correspondent': None,\n", "   'document_type': None,\n", "   'storage_path': None,\n", "   'title': '25000402620250127320421',\n", "   'content': 'LIDL & Cia - MONTEMOR-O-VELHO\\nAntiga EN 111 - Montemor-o-Veho\\nNIF:503340855 C.S. 498.880 EUR\\nRua Pé de Mouro 18,2714-510 Sintra\\nC.R.C Sintra N10628 SIRPEE PT000048\\n----------------------------------\\n\\nFATURA SIMPLIFICADA\\nOriginal Data de Venda: 2025-01-27\\nNo : FS 040200624/198501\\nNIF...: *********\\n\\n----------------------------------\\nEUR\\nCOOKIES PEPITAS CHOCOLATE 1,19 A\\nDesconto Lidl Plus -1,19\\nFIAMBRE PA FINISSIMO 2,30 A\\n2 x 1,15\\nDesconto Lidl Plus -0,32\\n------------\\nFEIJA<PERSON> MANTEIGA FR. 1,70 A\\n2 x 0,85\\nALIMENTO CAO - CACA 1,69 A\\nMILHO DOCE 1,99 A\\nCROQUETES PARA CAO 4,99 A\\nPIZZA DE FRANGO 2,79 A\\nPIZZA BARBECUE 2,79 A\\nGELADO BAUNILHA COM NOZES 2,49 A\\nPANADO QUEIJO BRIE 2,59 A\\nPEPINO 0,54 B\\n0,330 kg x 1,65 EUR\\nEUR/kg\\nGOUDA OLD HOLLAND 3,79 B\\nPUDIM PROTEINA 2 SAB SIDE B 4,45 A\\n5 x 0,89\\nCEBOLA FRITA 1,25 A\\nPUDIM PROTEINA 2 SAB SIDE B 2,67 A\\n3 x 0,89\\nALHO SECO 250 G 1,59 B\\nTOMATE CHERRY 250 G 1,25 B\\nMINI PIZZA FIAMBRE 3,49 A\\nMIX TOMATE CHERRY 250 G 1,49 B\\nUVA BRANCA S GRAINHA 500 G 2,69 B\\n------------\\nTotal 46,22\\n============\\nMULTIBANCO 46,22\\n\\nTotal em descontos 1,51\\n\\nTaxa Base Imp. Val.Total Val.IVA\\nA 23% 28,35 34,87 6,52\\nB 6% 10,71 11,35 0,64\\n¦ Com o Lidl Plus poupou ¦\\n¦ 1,51 EUR ¦\\n\\n\\n\\n\\nATCUD: JJ2VJXCB-198501\\n\\n\\n\\n\\n000402 320421/\\n006 27.01.25 19:10\\n\\nt8kF-Processado por Programma\\ncertificado n.º 1050/AT\\n\\nLIDL AGRADECE\\nMONTEMOR O VELHO\\nTerminal Pagamento Automático: 01303612\\n2025-01-27 19:11:09 Per021 Tr368 Msg602\\n\\nVISA INTERNACIONAL\\nCARTAO: ****7266 TC:3E776C2293006974\\nA0000000031010 Visa\\nDEBIT\\nCOMPRA 46,22€\\nId.Estab:********** AUT:211981 VISA DB N\\n\\nGetnet\\n\\n\\n\\n\\nVisa payWave\\nAutenticação realizada pelo dispositivo\\nmóvel\\nCÓPIA CLIENTE\\n\\nPROCESSADO POR SIBS',\n", "   'tags': [],\n", "   'created': '2025-01-27T00:00:00Z',\n", "   'created_date': '2025-01-27',\n", "   'modified': '2025-05-17T00:15:40.400000+01:00',\n", "   'added': '2025-05-17T00:15:38.150710+01:00',\n", "   'deleted_at': None,\n", "   'archive_serial_number': None,\n", "   'original_file_name': '25000402620250127320421.pdf',\n", "   'archived_file_name': '2025-01-27 25000402620250127320421.pdf',\n", "   'owner': 3,\n", "   'user_can_change': True,\n", "   'is_shared_by_requester': True,\n", "   'notes': [],\n", "   'custom_fields': [{'value': False, 'field': 3}],\n", "   'page_count': 2,\n", "   'mime_type': 'application/pdf'},\n", "  {'id': 59,\n", "   'correspondent': None,\n", "   'document_type': 2,\n", "   'storage_path': 2,\n", "   'title': 'cd0e4b8c-04a2-40c2-acc2-bfa73e151059',\n", "   'content': 'EMPIFARMA - Produtos Farmacêuticos S.A. Zona Industrial de Montemor-o-Velho,Lote (s) nº 12 EMPIFARMA - Produtos Farmacêuticos S.A. Zona Industrial de Montemor-o-Velho,Lote (s) nº 12\\n504100050 3140-293 Montemor-o-Velho 504100050 3140-293 Montemor-o-Velho\\nMontemor-o-Velho Montemor-o-Velho\\n\\n\\nOriginal Duplicado\\nOriginal\\n\\n\\n\\nRecibo de Vencimentos Recibo de Vencimentos\\n\\nPeríodo Novembro Nome Ricardo Jorge <PERSON> Período Novembro Nome Ricardo <PERSON>\\nData Fecho 30-11-2019 N.º Mecan. 153 Data Fecho 30-11-2019 N.º Mecan. 153\\nVencimento 615,00 Categoria Operador Logística I Vencimento 615,00 Categoria Operador Logística I\\nVenc. / Hora 3,55 N.º Benef. 11923306880 Venc. / Hora 3,55 N.º Benef. 11923306880\\nN. Dias Mês: 20.00 N.º Contrib. 242914993 N. Dias Mês: 20.00 N.º Contrib. 242914993\\nDepartamento Departamento Operacional Departamento Departamento Operacional\\nSeguro Fidelidade Mundial AT62861513 Seguro Fidelidade Mundial AT62861513\\n\\nFaltas Retenção IRS Faltas Retenção IRS\\nAlim. Turno CDH CDD SDH SDD IRS Retido Total Remun. Alim. Turno CDH CDD SDH SDD IRS Retido Total Remun.\\n576,00 10.161,54 576,00 10.161,54\\n\\nCód. Data Descrição Remunerações Descontos Cód. Data Descrição Remunerações Descontos\\nR01 11-2019 Vencimento 615,00 R01 11-2019 Vencimento 615,00\\nR11 11-2019 Subsídio Alimentação - Dias Processamento 132,00 R11 11-2019 Subsídio Alimentação - Dias Processamento 132,00\\nR24 11-2019 Horário Nocturno 97,63 R24 11-2019 Horário Nocturno 97,63\\nR91 11-2019 Subsídio Alimentação-Sábado 6,60 R91 11-2019 Subsídio Alimentação-Sábado 6,60\\nH07 11-2019 Hora Suplementar a 150% (10,00 H.) 88,76 H07 11-2019 Hora Suplementar a 150% (10,00 H.) 88,76\\nD01 11-2019 Segurança Social 92,38 D01 11-2019 Segurança Social 92,38\\nD02 11-2019 IRS (8,30%) 69,00 D02 11-2019 IRS (8,30%) 69,00\\nR31 11-2019 Subsídio de Natal 615,00 R31 11-2019 Subsídio de Natal 615,00\\nD01 11-2019 Segurança Social 67,65 D01 11-2019 Segurança Social 67,65\\n\\n\\n\\n\\nTotal 1.554,99 229,03 Total 1.554,99 229,03\\nFormas de Pagamento: Formas de Pagamento:\\n% Remuneração Forma de Pagamento Moeda % Remuneração Forma de Pagamento Moeda\\nTotal Pago ( EUR ) 1.325,96 Total Pago ( EUR ) 1.325,96\\n100,00 Transferência EUR 100,00 Transferência EUR\\nTotal Pago ( EUR ) 1.325,96 Total Pago ( EUR ) 1.325,96\\n\\n\\n\\n\\nDeclaro que recebi a quantia constante neste recibo, Obs. Declaro que recebi a quantia constante neste recibo, Obs.\\n\\n\\n\\n\\n© PRIMAVERA BSS / Licença de: EMPIFARMA - PRODUTOS FARMACÊUTICOS, SA © PRIMAVERA BSS / Licença de: EMPIFARMA - PRODUTOS FARMACÊUTICOS, SA',\n", "   'tags': [7],\n", "   'created': '2019-11-30T00:00:00Z',\n", "   'created_date': '2019-11-30',\n", "   'modified': '2025-05-17T00:15:55.354203+01:00',\n", "   'added': '2025-05-17T00:15:52.604046+01:00',\n", "   'deleted_at': None,\n", "   'archive_serial_number': None,\n", "   'original_file_name': 'cd0e4b8c-04a2-40c2-acc2-bfa73e151059.pdf',\n", "   'archived_file_name': '2019-11-30 cd0e4b8c-04a2-40c2-acc2-bfa73e151059.pdf',\n", "   'owner': 3,\n", "   'user_can_change': True,\n", "   'is_shared_by_requester': True,\n", "   'notes': [],\n", "   'custom_fields': [{'value': False, 'field': 3}],\n", "   'page_count': 1,\n", "   'mime_type': 'application/pdf'}]}"]}, "execution_count": 136, "metadata": {}, "output_type": "execute_result"}], "source": ["nao_processados"]}, {"cell_type": "code", "execution_count": 3, "id": "9ff94a44", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"nome\": \"<PERSON>eide <PERSON>\",\n", "  \"nif\": \"*********\",\n", "  \"vencimento_base\": 955.0,\n", "  \"vencimento_hora\": 5.92,\n", "  \"valor_liquido\": 947.6,\n", "  \"valor_bruto\": 1114.16,\n", "  \"descontos_totais\": 166.56,\n", "  \"dias_trabalhados\": 20,\n", "  \"data_fecho\": \"2025-04-30\",\n", "  \"ano\": 2025,\n", "  \"mes\": 4,\n", "  \"entidade_id\": 0,\n", "  \"ativo\": true\n", "}\n", "None\n", "{\n", "  \"nome\": \"<PERSON>\",\n", "  \"id_beneficiario\": \"11923306880\",\n", "  \"departamento\": \"Operacional\",\n", "  \"seguro\": \"Fidelidade Mundial AT62861513 Se<PERSON>ro Fidelidade Mundial AT62861513\",\n", "  \"id_colaborador\": \"153\",\n", "  \"nif\": \"242914993\",\n", "  \"vencimento_base\": 0.0,\n", "  \"vencimento_hora\": 8.29,\n", "  \"valor_liquido\": 1325.96,\n", "  \"valor_bruto\": 1554.99,\n", "  \"descontos_totais\": 1554.99,\n", "  \"dias_trabalhados\": 20,\n", "  \"data_fecho\": \"2019-11-30\",\n", "  \"ano\": 2019,\n", "  \"mes\": 11,\n", "  \"entidade_id\": 0,\n", "  \"ativo\": true\n", "}\n"]}], "source": ["from pgsqlops.app.services.api_service import ApiService\n", "from pgsqlapi.app.extractors.recibos import ReciboNeideExtractor, ReciboRicardoExtractor\n", "import json\n", "api_service = ApiService()\n", "nao_processados = api_service.get_unprocessed_documents()\n", "# for documento in nao_processados.results:\n", "#     # recibo = ReciboNeideExtractor(documento.content, documento.original_file_name).run_extraction()\n", "#     # recibo = ReciboCreate(**recibo)\n", "#     # print(recibo)\n", "#     if documento.document_type == 2 and documento.tags == [8]:\n", "#         recibo = ReciboNeideExtractor(documento.content, documento.original_file_name).run_extraction()\n", "#         for key, value in recibo.items():\n", "#             print(f\"{key}: {value}\")\n", "for documento in nao_processados.results:\n", "    if documento.document_type == 2 and documento.tags == [8]:\n", "        pass\n", "        recibo = ReciboNeideExtractor(documento.content, documento.original_file_name).run_extraction()\n", "        print(json.dumps(recibo.model_dump(mode=\"json\", exclude_none=True), indent=2, ensure_ascii=False))\n", "        response_insert = api_service.create_recibo(recibo.model_dump(mode=\"json\", exclude_none=True))\n", "        print(response_insert)\n", "    elif documento.document_type == 2 and documento.tags == [7]:\n", "        recibo = ReciboRicardoExtractor(documento.content, documento.original_file_name).run_extraction()\n", "        print(json.dumps(recibo.model_dump(mode=\"json\", exclude_none=True), indent=2, ensure_ascii=False))\n", "        # response_insert = api_service.create_recibo(recibo.model_dump(mode=\"json\", exclude_none=True))"]}, {"cell_type": "code", "execution_count": 1, "id": "03e37840", "metadata": {}, "outputs": [{"data": {"text/plain": ["PaperlessModel(count=2, next=None, previous=None, all=[1, 15], results=[PaperlessDocument(id=1, correspondent=2, document_type=3, storage_path=3, title='FR 2025B_6535', content='A T C U D :JJ3G 26G Y -6535\\nFactura - R ecibo 2025B /6535\\n\\n\\n\\n\\nN eide D alila A zevedo A guiar\\n\\nR ua da Fonte N º151 C asal dos Silvas\\nM ontem or-o-velho\\n3140-298 M O N T EM O R -O -V ELH O\\n\\n\\n\\n\\nB ox N 111 N IF:*********\\nB ox N 111, Lda\\nEstrada N acional 111 N º Insc.: 1685 N º Sócio: 1685\\nLote 32/33 - Fração C\\nParque de N egócios\\n3140-274 M ontem or-o-V elho D ata:2025-05-07 H ora:17:52:12\\nT elef.: 963 776 034\\nC usto cham ada para rede m óvel nacional\\nN º C ontribuinte: 514271094 D ata de V encim ento:2025-05-07 Página 1 de 1\\nSoc. por Q uotas - C ap. Social: 1000€\\nC .R .C . M ontem or-o-V elho\\nO R IG IN A L\\nE-m ail: boxn111m m v@ gm ail.com\\n\\n\\n\\n\\nC ódigo D escrição Q uant. D esc. IV A Preço unitário Total\\n\\nD V 183602 Pag (Início de T ransf.) Seguro 1, 0,00% 0% 15,00€ 15,00€\\n\\n* Isento artigo 9.º do C IV A\\n\\nD V 183604 (28/04/2025) A cesso Livre - Fid - Q uinzenal 2025 1, 50,00% 23% 22,68€ 13,95€\\n\\nD V 183603 (Pagam ento Pontual) Inscrição 1, 70,00% 23% 40,65€ 15,00€\\n\\n\\n\\n\\nIva Incidência V alor T otal ilíquido: 78,33€\\n\\nT otal descontos: 39,80€\\n0,00% 15,00€ 0,00€\\nT otal IV A : 5,41€\\n23,00% 23,54€ 5,41€\\nA rredondam ento: 0,01€\\n\\nTotaldo D ocum ento: 43,95€\\n\\n\\noper: C ristiana form a pag.: M ultibanco\\ni+ K 4-Processado por program a certificado nº577/A T\\n\\nO brigada pela sua preferência!\\n\\n\\n\\n\\nR ecebem os a quantia de quarenta e três euros e noventa e cinco cêntim os\\n\\n\\nA G erência\\n\\n\\n\\n\\nEste docum ento é válido com o R EC IB O após boa cobrança.\\n\\nA rquivandus (c)2000-2025 SportStudio 3.8', tags=[], created='2025-05-07', created_date='2025-05-07', modified='2025-05-27T05:01:29.577682+01:00', added='2025-05-23T11:16:30.112891+01:00', deleted_at=None, archive_serial_number=None, original_file_name='FR 2025B_6535.PDF', archived_file_name='2025-05-07 neide FR 2025B_6535.pdf', owner=3, user_can_change=True, is_shared_by_requester=True, notes=[], custom_fields=[CustomField(value=False, field=1)], page_count=1, mime_type='application/pdf'), PaperlessDocument(id=15, correspondent=1, document_type=3, storage_path=3, title='25000402620250418432437', content='LIDL & Cia - MONTEMOR-O-VELHO\\nAntiga EN 111 - Montemor-o-Veho\\nNIF:503340855 C.S. 498.880 EUR\\nRua Pé de Mouro 18,2714-510 Sintra\\nC.R.C Sintra N10628 SIRPEE PT000048\\n----------------------------------\\n\\nFATURA SIMPLIFICADA\\nOriginal Data de Venda: 2025-04-18\\nNo : FS 040200625/030298\\nNIF...: 242914993\\n\\n----------------------------------\\nEUR\\nFIAMBRE PA FINISSIMO 2,58 A\\n2 x 1,29\\nDESCONTO 20% -0,52\\n------------\\nSACO DE PAPEL 0,15 A\\nLEITE MAGRO SEM LACTOSE 0,99 B\\nUVA BRANCA S GRAINHA 500 G 3,98 B\\n2 x 1,99\\nALHEIRA COM CACA 1,59 C\\nALHEIRA DE GALO 1,39 C\\nQUEIJO FLAMENGO FATIADO P F 3,99 B\\nBIO ABACATE 2 UNIDADES 2,19 B\\nBARRA PROTEINA PACK3 COOKIE 3,15 A\\nPIMENTO VERMELHO 500 G 2,19 B\\nPEPINO 0,41 B\\n0,218 kg x 1,89 EUR\\nEUR/kg\\n------------\\nTotal 22,09\\n============\\nMULTIBANCO 22,09\\n\\nTotal em descontos 0,52\\n\\nTaxa Base Imp. Val.Total Val.IVA\\nA 23% 4,36 5,36 1,00\\nB 6% 12,97 13,75 0,78\\nC 13% 2,64 2,98 0,34\\n\\n\\n\\n\\nATCUD: JJY9FMPS-030298\\n000402 432437/\\n006 18.04.25 16:49\\n\\nQHvP-Processado por Programma\\ncertificado n.º 1050/AT\\n\\nLIDL AGRADECE\\nMONTEMOR O VELHO\\nTerminal Pagamento Automático: 01303612\\n2025-04-18 16:50:21 Per068 Tr299 Msg027\\n\\nVISA INTERNACIONAL\\nCARTAO: ****7266 TC:82F3133522A83470\\nA0000000031010 Visa DEBIT\\nCOMPRA 22,09€\\nId.Estab:********** AUT:651048 VISA DB N\\n\\nGetnet\\n\\n\\n\\n\\nVisa payWave\\nAutenticação realizada pelo dispositivo\\nmóvel\\nCÓPIA CLIENTE\\n\\nPROCESSADO POR SIBS', tags=[], created='2025-04-18', created_date='2025-04-18', modified='2025-05-27T22:26:41.834512+01:00', added='2025-05-27T22:26:41.456524+01:00', deleted_at=None, archive_serial_number=None, original_file_name='25000402620250418432437.pdf', archived_file_name='2025-04-18 ricardo 25000402620250418432437.pdf', owner=3, user_can_change=True, is_shared_by_requester=True, notes=[], custom_fields=[CustomField(value=False, field=1), CustomField(value='8JDNoPvStBm8xf2j', field=2)], page_count=2, mime_type='application/pdf')])"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from pgsqlops.app.services.api_service import ApiService\n", "from comum.services.logger import setup_logger\n", "logger = setup_logger()\n", "api_service = ApiService()\n", "nao_processados = api_service.get_unprocessed_documents()\n", "# tag2023 = api_service.get_tag_by_name(tag_name=\"2023\")\n", "# if tag2023:\n", "#     tag_id = tag2023.all[0]\n", "#     print(tag_id)\n", "nao_processados"]}, {"cell_type": "code", "execution_count": 6, "id": "380f7f67", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["id=1 correspondent=2 document_type=3 storage_path=None title='FR 2025B_6535' content='A T C U D :JJ3G 26G Y -6535\\nFactura - R ecibo 2025B /6535\\n\\n\\n\\n\\nN eide D alila A zevedo A guiar\\n\\nR ua da Fonte N º151 C asal dos Silvas\\nM ontem or-o-velho\\n3140-298 M O N T EM O R -O -V ELH O\\n\\n\\n\\n\\nB ox N 111 N IF:*********\\nB ox N 111, Lda\\nEstrada N acional 111 N º Insc.: 1685 N º Sócio: 1685\\nLote 32/33 - Fração C\\nParque de N egócios\\n3140-274 M ontem or-o-V elho D ata:2025-05-07 H ora:17:52:12\\nT elef.: 963 776 034\\nC usto cham ada para rede m óvel nacional\\nN º C ontribuinte: 514271094 D ata de V encim ento:2025-05-07 Página 1 de 1\\nSoc. por Q uotas - C ap. Social: 1000€\\nC .R .C . M ontem or-o-V elho\\nO R IG IN A L\\nE-m ail: boxn111m m v@ gm ail.com\\n\\n\\n\\n\\nC ódigo D escrição Q uant. D esc. IV A Preço unitário Total\\n\\nD V 183602 Pag (Início de T ransf.) Seguro 1, 0,00% 0% 15,00€ 15,00€\\n\\n* Isento artigo 9.º do C IV A\\n\\nD V 183604 (28/04/2025) A cesso Livre - Fid - Q uinzenal 2025 1, 50,00% 23% 22,68€ 13,95€\\n\\nD V 183603 (Pagam ento Pontual) Inscrição 1, 70,00% 23% 40,65€ 15,00€\\n\\n\\n\\n\\nIva Incidência V alor T otal ilíquido: 78,33€\\n\\nT otal descontos: 39,80€\\n0,00% 15,00€ 0,00€\\nT otal IV A : 5,41€\\n23,00% 23,54€ 5,41€\\nA rredondam ento: 0,01€\\n\\nTotaldo D ocum ento: 43,95€\\n\\n\\noper: C ristiana form a pag.: M ultibanco\\ni+ K 4-Processado por program a certificado nº577/A T\\n\\nO brigada pela sua preferência!\\n\\n\\n\\n\\nR ecebem os a quantia de quarenta e três euros e noventa e cinco cêntim os\\n\\n\\nA G erência\\n\\n\\n\\n\\nEste docum ento é válido com o R EC IB O após boa cobrança.\\n\\nA rquivandus (c)2000-2025 SportStudio 3.8' tags=[] created='2025-05-07' created_date='2025-05-07' modified='2025-05-23T11:19:05.412192+01:00' added='2025-05-23T11:16:30.112891+01:00' deleted_at=None archive_serial_number=None original_file_name='FR 2025B_6535.PDF' archived_file_name='2025-05-07 neide FR 2025B_6535.pdf' owner=3 user_can_change=True is_shared_by_requester=True notes=[] custom_fields=[CustomField(value=False, field=1)] page_count=1 mime_type='application/pdf'\n"]}], "source": ["exemplo = nao_processados.results[0]\n", "print(exemplo)"]}, {"cell_type": "code", "execution_count": 2, "id": "c063a594", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["count=1 next=None previous=None all=[11] results=[PaperlessTag(id=11, slug='lidl', name='lidl', color='#2e3cba', text_color='#ffffff', match='', matching_algorithm=0, is_insensitive=True, is_inbox_tag=False, document_count=1, owner=3, user_can_change=True)]\n"]}], "source": ["tag = api_service.get_tag_by_name(tag_name=\"lidl\")\n", "print(tag)\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.0"}}, "nbformat": 4, "nbformat_minor": 5}