import streamlit as st
import pandas as pd
from src.services.supabase_client import connect, executa_query
from src.faturas.classFatura import <PERSON><PERSON><PERSON><PERSON>,Lin<PERSON>, Fatura


supabase = connect()

entidades = ["-", "lidl", "microsoft", "via_verde"]




def initialize_session_state():
    if "ano_selected" not in st.session_state:
        st.session_state["ano_selected"] = None
    if "entidade" not in st.session_state:
        st.session_state["entidade"] = None



def get_anos():
    anos = supabase.table('faturas_cabec').select('ano').execute().data
    if anos:
        anos_distintos = list(set(int(ano['ano']) for ano in anos))  
        anos_distintos.sort() 
    else:
        anos_distintos = []
    return anos_distintos


def get_linhas(id_chave):
    response = supabase.table("faturas_linhas").select("*").eq("id_chave_doc", id_chave).execute()
    return response.data


def get_fatura(id_chave):
    response = supabase.table("faturas_cabec").select("*").eq("id_chave", id_chave).execute()
    cabec = Cabecalho(**response.data[0])
    response = supabase.table("faturas_linhas").select("*").eq("id_chave_doc", id_chave).execute()
    linhas = [Linhas(**linha) for linha in response.data]
    fatura = Fatura(cabec=cabec, linhas=linhas)
    return fatura


def main():
    st.title("FATURAS")
    with st.sidebar:
        entidade = st.selectbox("Selecionar entidade:", entidades, index=0)
        ano = st.selectbox("Selecionar ano:", get_anos(), index=0)

    if entidade and entidade != "-":
        st.session_state['entidade'] = entidade
        df = executa_query(id_query=8, query_params={'entidade': [entidade], 'ano': [ano]})
        id_chaves = df["id_chave"].copy()
        total_valor = df["valor"].sum()
        st.metric("Total", total_valor, border=True)
        st.selectbox
        st.dataframe(df, height=300)
        



        # linha_select = st.dataframe(df, on_select="rerun", selection_mode="single-row", height=300, use_container_width=True)
        # if linha_select["selection"]["rows"]:
        #     id_chave_selecionado = id_chaves[linha_select["selection"]["rows"][0]]
        #     cabec = get_cabec(id_chave_selecionado)
        #     cabec = Cabecalho(**cabec[0])
        #     df_cabec = Cabecalho.get_tabela([cabec])
        #     linhas = get_linhas(id_chave_selecionado)
        #     linhas =  [Linhas(**linha) for linha in linhas]
        #     df_linhas = Linhas.get_tabela(linhas)
        #     df_linhas = df_linhas.drop(columns=["id_chave", "id_chave_doc", "data_criacao", "data_alteracao", "data", "fatura"])
        #     st.dataframe(df_cabec, height=100)
        #     st.dataframe(df_linhas)



if __name__ == "__main__":
    main()