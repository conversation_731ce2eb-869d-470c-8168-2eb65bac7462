from datetime import date, datetime
import re
from uuid import UUID
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Type



class Telefone(BaseModel):
    numero: str
    tipo: Optional[str] = None

class Email(BaseModel):
    mail: Optional[str] = None 
    tipo: Optional[str] = None



class Phone(BaseModel):
    field: Optional[str] = None
    label: Optional[str] = None

class IMService(BaseModel):
    IMService: Optional[str] = None
    userName: Optional[str] = None

class Url(BaseModel):
    field: Optional[str] = None
    label: Optional[str] = None

class Photo(BaseModel):
    signature: Optional[str] = None
    url: Optional[str] = None
    crop: Dict[str, int]

class Mail(BaseModel):
    field: Optional[str] = None
    label: Optional[str] = None
class Profile(BaseModel):
    field: Optional[str] = None
    user: Optional[str] = None
    userId: Optional[str] = None

class ContactoRaw(BaseModel):
    firstName: Optional[str] = None
    lastName: Optional[str] = None
    isGuardianApproved: bool
    contactId: Optional[UUID] = None
    normalized: Optional[str] = None
    photo: Optional[Photo] = None
    phones: Optional[List[Phone]] = []
    etag: Optional[str] = None
    whitelisted: Optional[bool] = False
    isCompany: Optional[bool] = False
    birthday: Optional[date] = None
    companyName: Optional[str] = None
    ims: Optional[List[IMService]] = None
    emailAddresses: Optional[List[Dict[str, str]]] = None
    urls: Optional[List[Url]] = None





class ContactoClean(BaseModel):
    id: Optional[UUID] = Field(default=None)
    id_chave: Optional[UUID] = Field(default=None)
    nome: Optional[str] = None
    apelido: Optional[str] = None
    entidade: Optional[str] = None
    telefones: Optional[List[Phone]] = None
    mails: Optional[List[Dict[str, str]]] = None
    origem: Optional[str] = None
    ativo: Optional[bool] = True
    data_criacao: Optional[datetime] = None
    data_alteracao: Optional[datetime] = None

    @classmethod
    def clean(cls, raw: ContactoRaw):
        def normalize_phone(phone: Phone) -> Phone:
            padrao = r"[^\d+]"
            phone.field = re.sub(padrao, "", phone.field if phone.field else "")
            phone.field = phone.field[-9:] if phone.field else ""
            return phone        
        cleaned_phones = ([normalize_phone(phone) for phone in raw.phones] if raw.phones else None)
        return cls(
            id_chave=raw.contactId,
            nome=raw.firstName,
            apelido=raw.lastName,
            telefones=cleaned_phones,
            mails=raw.emailAddresses,
            entidade=raw.companyName
            )
    
    @classmethod
    def sort_by_nome(cls, contatos_clean: List[Type['ContactoClean']]) -> List[Type['ContactoClean']]:
        return sorted(contatos_clean, key=lambda c: (c.nome or '').lower())
    


    def dict(self, *args, **kwargs) -> dict:
        original_dict = super().model_dump(*args, **kwargs)
        for key, value in original_dict.items():
            if isinstance(value, datetime):
                original_dict[key] = value.isoformat()
        return original_dict


class ContactoTelefone(BaseModel):
    id: Optional[UUID] = Field(default=None)
    id_chave: Optional[UUID] = Field(default=None)
    contacto_id: Optional[UUID] = Field(default=None)
    telefone: Optional[str] = Field(default=None)
    tipo: Optional[str] = Field(default=None)
    cont_trf: Optional[str] = Field(default=None)
    ativo: Optional[bool] = Field(default=True)
    data_criacao: Optional[datetime] = Field(default=None)
    data_alteracao: Optional[datetime] = Field(default=None)