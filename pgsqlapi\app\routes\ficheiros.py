from typing import List
from sqlmodel import Session
from fastapi import APIRouter, Depends
from comum.models.ficheiros import FicheiroBase, FicheiroCreate, FicheiroUpdate
from app.database.postgres import get_session
from app.workers.ficheiros import FicheirosWorker
from uuid import UUID


ficheiros_router = APIRouter()

def get_worker(session: Session = Depends(get_session)) -> FicheirosWorker:
    return FicheirosWorker(session)

@ficheiros_router.get("/ficheiros", response_model=List[FicheiroBase])
async def lista_completa(worker: FicheirosWorker = Depends(get_worker)):
    return list(worker.lista())

@ficheiros_router.get("/ficheiros/{fkid_chave}", response_model=FicheiroBase)
async def procura_ficheiro(fkid_chave: UUID, worker: FicheirosWorker = Depends(get_worker)):
    return worker.procura_ficheiro(fkid_chave)

@ficheiros_router.post("/ficheiros", response_model=FicheiroBase)
async def cria(novos_dados: FicheiroCreate, worker: FicheirosWorker = Depends(get_worker)):
    return worker.cria(novos_dados)

@ficheiros_router.patch("/ficheiros/{id}", response_model=FicheiroBase)
async def atualiza(id: int, dados: FicheiroUpdate, worker: FicheirosWorker = Depends(get_worker)):
    return worker.atualiza(id, dados)
    

@ficheiros_router.delete("/ficheiros/{id}")
async def remove(id: int, worker: FicheirosWorker = Depends(get_worker)):
    worker.remove(id)
    return {"message": f"ID: **{id}** deleted!"}