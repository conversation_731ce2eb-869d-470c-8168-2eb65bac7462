import streamlit as st
from src.recibos.workerRecibo import WorkerRecibos
from src.logs.classLogsApp import LogApp
from src.recibos.classRecibo import <PERSON>ci<PERSON>


def set_downloaded():
    st.session_state["downloaded"] = True

def initialize_session_state():
    if "analisar" not in st.session_state:
        st.session_state["analisar"] = False
    if "upload" not in st.session_state:
        st.session_state["upload"] = False
    if "downloaded" not in st.session_state:
        st.session_state["downloaded"] = False
    if "selected_user" not in st.session_state:
        st.session_state["selected_user"] = None



def show_pdf(base64_pdf):
    pdf_display = f'<iframe src="data:application/pdf;base64,{base64_pdf}" width="800" height="800" type="application/pdf"></iframe>'
    st.markdown(pdf_display, unsafe_allow_html=True)


def analisador(uploaded_files, utilizador):
    progress_bar = st.sidebar.progress(0)
    status_text = st.sidebar.empty()
    total_files = len(uploaded_files)


    for index, file in enumerate(uploaded_files, start=1):
        with st.expander(f"Arquivo ({index}): {file.name}"):
            try:
                response = WorkerRecibos().processar_ficheiro(ficheiro=file, utilizador=utilizador, save=False)
                if isinstance(response, str) and response.startswith("Erro"):
                    st.error(response)
                else:
                    st.json(response)
            except Exception as e:
                st.error(f"Erro ao processar o arquivo {file.name}: {e}")

        progress = int((index / total_files) * 100)
        progress_bar.progress(progress)
        status_text.text(f"Analisado: {index}/{total_files}")



def uploader(uploaded_files, utilizador):
    st.session_state["downloaded"] = False    
    progress_bar = st.sidebar.progress(0)
    status_text = st.sidebar.empty()
    total_files = len(uploaded_files)
    files_toget = []


    for index, pdf in enumerate(uploaded_files, start=1):
        with st.expander(f"Arquivo: {pdf.name}"):
            try:
                response = WorkerRecibos().processar_ficheiro(ficheiro=pdf, utilizador=utilizador, save=True)
                if isinstance(response, str) and response.startswith("Erro"):
                    raise Exception(response)
                else:
                    recibo = Recibo(**response.json())
                    st.write(recibo)
                    # response = api_get(tabela=f"ficheiros/{recibo.id_chave}")
                    # ficheiro = Ficheiro(**response.json())
                    # # show_pdf(ficheiro.binario)
                    id_chave = recibo.id_chave
                    novo_nome = f"{id_chave}.pdf"
                    files_toget.append((novo_nome, pdf.getvalue()))
                progress = int((index / total_files) * 100)
                progress_bar.progress(progress)
                status_text.text(f"Enviado: {index}/{total_files}")
                
            except Exception as e:
                st.error(e)
                status_text.text("ERRO")

    if files_toget:
        zip_data = WorkerRecibos.create_zip(files_toget)
        st.sidebar.download_button(label="download", data=zip_data, file_name="recibos.zip", mime="application/zip", on_click=set_downloaded)








def main():
    initialize_session_state()


    st.title("Upload de Recibos")
    utilizador = st.session_state["selected_user"]
    uploaded_files = st.file_uploader("Selecione os recibos", type=["pdf"], accept_multiple_files=True)
    if uploaded_files:
        st.session_state.downloaded = False
        st.session_state.analisar = False
        st.session_state.upload = False
        st.sidebar.write(f'{len(uploaded_files)} arquivo(s)')
        total_files = len(uploaded_files)
        col1, col2 = st.columns(2)
        with st.sidebar:
            with col1:
                if st.sidebar.button("analisar"):
                    st.session_state["analisar"] = True
                    st.session_state["upload"] = False

            with col2:
                if st.sidebar.button("upload"):
                    st.session_state["upload"] = True
                    st.session_state["analisar"] = False

        if st.session_state["analisar"]:
            analisador(uploaded_files, utilizador)

        if st.session_state["upload"]:
            uploader(uploaded_files, utilizador)

            

        if st.session_state["downloaded"]:
            LogApp.log_warning(
                                classe="recibos",
                                operacao="download",
                                resultado={"file_name": "recibos.zip", "num_files": total_files}
                            )

if __name__ == "__main__":
    main()
