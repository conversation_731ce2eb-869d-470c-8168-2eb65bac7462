from sqlmodel import SQLModel, Field, Relationship
from typing import Optional
from datetime import datetime
from sqlalchemy.dialects.postgresql import UUID as PG_UUID, JSONB
from sqlalchemy import Column, text, Boolean, DateTime, String
from uuid import UUID


class Contacto(SQLModel, table=True):
    __tablename__ = "contactos"
    id: int = Field(primary_key=True, sa_column_kwargs={"autoincrement": True})
    id_chave: UUID = Field(sa_column=Column(PG_UUID(as_uuid=True), nullable=False, unique=True, server_default=text("gen_random_uuid()")))
    nome: str = Field(sa_column=Column(type_=String, nullable=True))
    apelido: str = Field(sa_column=Column(type_=String, nullable=True))
    entidade: str = Field(sa_column=Column(type_=String, nullable=True))
    telefones: dict = Field(sa_column=Column(JSONB, nullable=True))
    mails: dict = Field(sa_column=Column(JSONB, nullable=True))
    origem: str = Field(sa_column=Column(type_=String, nullable=True))
    ativo: bool = Field(sa_column=Column(Boolean, nullable=False, server_default=text("true")))
    data_criacao: datetime = Field(sa_column=Column(type_=DateTime, nullable=False, server_default=text("now()")))
    data_alteracao: datetime = Field(sa_column=Column(type_=DateTime, nullable=False, server_default=text("now()")))

    telefones_rel: list["ContactoTelefone"] = Relationship(back_populates="contacto_rel") # type: ignore

class ContactoCreate(SQLModel):
    nome: str
    apelido: Optional[str] = Field(default=None)
    entidade: Optional[str] = Field(default=None)
    telefones: Optional[dict] = Field(default=None)
    mails: Optional[dict] = Field(default=None)
    origem: Optional[str] = Field(default=None)
    ativo: Optional[bool] = Field(default=True)

    class Config:
        json_schema_extra = {
            "example": {
                "nome": "teste_nome",
                "apelido": "teste_apelido",
                "entidade": "teste_entidade",
                "telefones": {"home": "999999999", "work": "888888888", "mobile": "777777777"},
                "mails": {"personal": "<EMAIL>", "work": "<EMAIL>"},
                "origem": "teste_origem",
                "ativo": True
            }
        }


class ContactoUpdate(SQLModel):
    nome: Optional[str] = Field(default=None)
    apelido: Optional[str] = Field(default=None)
    entidade: Optional[str] = Field(default=None)
    telefones: Optional[dict] = Field(default=None)
    mails: Optional[dict] = Field(default=None)
    origem: Optional[str] = Field(default=None)
    ativo: Optional[bool] = Field(default=None)


class ContactoBase(SQLModel):
    id: int = Field(default=None)
    id_chave: UUID = Field(default=None)
    nome: str = Field(default=None)
    apelido: str = Field(default=None)
    entidade: str = Field(default=None)
    telefones: Optional[dict] = Field(default=None)
    mails: Optional[dict] = Field(default=None)
    origem: Optional[str] = Field(default=None)
    ativo: bool = Field(default=None)
    data_criacao: datetime = Field(default=None)
    data_alteracao: datetime = Field(default=None)
