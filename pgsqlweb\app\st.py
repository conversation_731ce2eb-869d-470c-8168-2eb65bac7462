import streamlit as st
import httpx

st.title("Enviar documentos para o Paperless-ngx")

url = "http://************:5001/api/documents/post_document/"
headers = {
    'Authorization': 'Token cca4c7bffdd41442417ca16f41a88fbf2fd85335',
}

uploaded_files = st.file_uploader("Selecione os PDFs", type=["pdf"], accept_multiple_files=True)

if uploaded_files:
    for f in uploaded_files:
        st.write(f"Arquivo pronto: {f.name}")

if st.button("Enviar"):
    for file in uploaded_files:
        file.seek(0)  # Garante que o ponteiro está no início

        files = {
            'document': (file.name, file, 'application/pdf')
        }

        try:
            response = httpx.post(url, headers=headers, files=files)
            st.write(f"{file.name} - Status {response.status_code}")
            st.write(response.text)
        except Exception as e:
            st.error(f"Erro ao enviar {file.name}: {e}")