from typing import List
from sqlmodel import Session
from fastapi import APIRouter, Depends
from comum.models.recibos_codigos import ReciboCodigoBase, ReciboCodigoCreate, ReciboCodigoUpdate
from app.database.postgres import get_session
from app.workers.recibos_codigos import RecibosCodigosWorker


recibos_codigos_router = APIRouter()

def get_worker(session: Session = Depends(get_session)) -> RecibosCodigosWorker:
    return RecibosCodigosWorker(session)


@recibos_codigos_router.get("/recibos_codigos", response_model=List[ReciboCodigoBase])
async def lista_completa(worker: RecibosCodigosWorker = Depends(get_worker)):
    return list(worker.lista())


@recibos_codigos_router.get("/recibos_codigos/{id}", response_model=ReciboCodigoBase)
async def procura(id: int, worker: RecibosCodigosWorker = Depends(get_worker)):
    return worker.procura(id)


@recibos_codigos_router.post("/recibos_codigos", response_model=ReciboCodigoBase)
async def cria(novos_dados: ReciboCodigoCreate, worker: RecibosCodigosWorker = Depends(get_worker)):
    return worker.cria(novos_dados)


@recibos_codigos_router.patch("/recibos_codigos/{id}", response_model=ReciboCodigoBase)
async def atualiza(id: int, dados: ReciboCodigoUpdate, worker: RecibosCodigosWorker = Depends(get_worker)):
    return worker.atualiza(id, dados)
    

@recibos_codigos_router.delete("/recibos_codigos/{id}")
async def remove(id: int, worker: RecibosCodigosWorker = Depends(get_worker)):
    worker.remove(id)
    return {"message": f"ID: **{id}** deleted!"}