import zipfile
import io
import streamlit as st
from src.faturas.extractorViaVerde import FaturaViaVerdeExtractor
from src.services.supabase_client import supabase_insert,supabase_storage_upload, generate_link

bucket = 'documentos/faturas/via_verde'
def main():
    st.title("VIA VERDE")

    uploaded_files = st.file_uploader("Faturas Via Verde", type=["xml"], accept_multiple_files=True)
    if uploaded_files:
        st.info(f"Arquivos detectados: {len(uploaded_files)}")

        if st.button("Analisar"):
            for xml in uploaded_files:
                with st.expander(f'Ficheiro: {xml.name}'):
                    fatura = FaturaViaVerdeExtractor(xml).run_extraction()
                    df_cabec, df_linhas = fatura.criar_dfs()
                    st.dataframe(df_cabec)
                    st.dataframe(df_linhas)


        arquivos_para_zip = []

        # Processar arquivos
        if st.button(f'Upload {len(uploaded_files)} files'):
            for xml in uploaded_files:
                col1, col2, col3 = st.columns([3, 1, 1])

                with col1:
                    with st.expander(f'Ficheiro: {xml.name}'):
                        fatura = FaturaViaVerdeExtractor(xml).run_extraction()
                        insert_cabec = supabase_insert(tabela='faturas_cabec', dados=[fatura.cabecalho.dict(exclude_unset=True)])
                        novo_nome = supabase_storage_upload(bucket=bucket, uploaded_file=xml, file_type='xml', insert_result=insert_cabec)
                        # nome2 = str(fatura.cabecalho.mes) + str(fatura.cabecalho.ano) + '.xml'

                        id_chave_doc = insert_cabec[0].id_chave
                        linhas_dict = []
                        for linha in fatura.linhas:
                            linha.id_chave_doc = id_chave_doc
                            linha_dict = linha.dict(exclude_unset=True)
                            linha_dict["id_chave_doc"] = str(linha_dict["id_chave_doc"])
                            linhas_dict.append(linha_dict)
                        insert_linhas = supabase_insert(tabela='faturas_linhas', dados=linhas_dict)
                        update_result = generate_link(bucket=bucket, novo_nome=novo_nome, tabela='faturas_cabec', insert_result=insert_cabec)
                        link = update_result[0].link
                        arquivos_para_zip.append((novo_nome, xml.getvalue()))
                with col2:
                    if fatura:
                        st.success("OK")
                with col3:
                    st.link_button('XML', link)
                    
        if arquivos_para_zip:
            with io.BytesIO() as zip_buffer:
                with zipfile.ZipFile(zip_buffer, "w") as zf:
                    for nome_arquivo, conteudo in arquivos_para_zip:
                        zf.writestr(nome_arquivo, conteudo)
                zip_buffer.seek(0)

                st.download_button(
                    label="DOWNLOAD.zip",
                    data=zip_buffer.getvalue(),
                    file_name="faturas_via_verde.zip",
                    mime="application/zip"
                )





if __name__ == "__main__":
    main()
