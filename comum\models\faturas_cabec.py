from sqlmodel import SQLModel, <PERSON>, Relationship
from typing import Optional
from datetime import datetime
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy import Column, text, Boolean, DateTime, String, Float, Integer
from uuid import UUID


class FaturaCabec(SQLModel, table=True):
    __tablename__ = "faturas_cabec"
    id: int = Field(primary_key=True, sa_column_kwargs={"autoincrement": True})
    id_chave: UUID = Field(sa_column=Column(PG_UUID(as_uuid=True), nullable=False, unique=True, server_default=text("gen_random_uuid()")))
    fatura: str = Field(sa_column=Column(type_=String, nullable=True))
    nif: str = Field(sa_column=Column(type_=String, nullable=True))
    nif_entidade: str = Field(sa_column=Column(type_=String, nullable=True))
    base: float = Field(sa_column=Column(type_=Float, nullable=True, server_default=text("0.0")))
    iva: float = Field(sa_column=Column(type_=Float, nullable=True, server_default=text("0.0")))
    desconto: float = Field(sa_column=Column(type_=Float, nullable=True, server_default=text("0.0")))
    valor: float = Field(sa_column=Column(type_=Float, nullable=True, server_default=text("0.0")))
    valor_bruto: float = Field(sa_column=Column(type_=Float, nullable=True, server_default=text("0.0")))
    entidade_id: int = Field(foreign_key="entidades.id")
    link: str = Field(sa_column=Column(type_=String, nullable=True))
    data: datetime = Field(sa_column=Column(type_=DateTime, nullable=False))
    ano: int = Field(sa_column=Column(type_=Integer, nullable=True))
    mes: int = Field(sa_column=Column(type_=Integer, nullable=True))
    ativo: bool = Field(sa_column=Column(Boolean, nullable=False, server_default=text("true")))
    data_criacao: datetime = Field(sa_column=Column(type_=DateTime, nullable=False, server_default=text("now()")))
    data_alteracao: datetime = Field(sa_column=Column(type_=DateTime, nullable=False, server_default=text("now()")))

    entidade_rel: Optional["Entidade"] = Relationship(back_populates="faturas_cabec_rel") # type: ignore
    faturas_linhas_rel: Optional["FaturaLinhas"] = Relationship(back_populates="fatura_cabec_rel") # type: ignore

class FaturaCabecCreate(SQLModel):
    fatura: Optional[str] = Field(default=None)
    nif: Optional[str] = Field(default=None)
    nif_entidade: Optional[str] = Field(default=None)
    base: float = Field(default=0.0)
    iva: float = Field(default=0.0)
    desconto: float = Field(default=0.0)
    valor: float = Field(default=0.0)
    valor_bruto: float = Field(default=0.0)
    entidade_id: int = Field(default=0)
    link: Optional[str] = Field(default=None)
    data: datetime = Field(default=None)
    ano: Optional[int] = Field(default=None)
    mes: Optional[int] = Field(default=None)
    ativo: Optional[bool] = Field(default=True)

    class Config:
        json_schema_extra = {
            "example": {
                "fatura": "teste_fatura",
                "nif": "999999999",
                "nif_entidade": "999999999",
                "base": 100.0,
                "iva": 23.0,
                "desconto": 0.0,
                "valor": 123.0,
                "valor_bruto": 123.0,
                "entidade_id": 1,
                "link": "teste_link",
                "data": "2000-01-01T00:00:00",
                "ano": 2000,
                "mes": 1,
                "ativo": True
            }
        }

class FaturaCabecUpdate(SQLModel):
    fatura: Optional[str] = Field(default=None)
    nif: Optional[str] = Field(default=None)
    nif_entidade: Optional[str] = Field(default=None)
    base: Optional[float] = Field(default=None)
    iva: Optional[float] = Field(default=None)
    desconto: Optional[float] = Field(default=None)
    valor: Optional[float] = Field(default=None)
    valor_bruto: Optional[float] = Field(default=None)
    entidade_id: Optional[int] = Field(default=None)
    link: Optional[str] = Field(default=None)
    data: Optional[datetime] = Field(default=None)
    ano: Optional[int] = Field(default=None)
    mes: Optional[int] = Field(default=None)
    ativo: Optional[bool] = Field(default=None)

class FaturaCabecBase(SQLModel):
    id: int = Field(default=None)
    id_chave: UUID = Field(default=None)
    fatura: str = Field(default=None)
    nif: str = Field(default=None)
    nif_entidade: str = Field(default=None)
    base: float = Field(default=0.0)
    iva: float = Field(default=0.0)
    desconto: float = Field(default=0.0)
    valor: float = Field(default=0.0)
    valor_bruto: float = Field(default=0.0)
    entidade_id: Optional[int] = Field(default=None)
    link: Optional[str] = Field(default=None)
    data: Optional[datetime] = Field(default=None)
    ano: Optional[int] = Field(default=None)
    mes: Optional[int] = Field(default=None)
    ativo: bool = Field(default=None)
    data_criacao: datetime = Field(default=None)
    data_alteracao: datetime = Field(default=None)
