from datetime import datetime
from typing import Any, List, Optional
from uuid import UUID
import pandas as pd
from pydantic import BaseModel, Field


class Recibo(BaseModel):
    id_chave: Optional[UUID] = Field(default=None, description="Identificador único do recibo")
    data_criacao: Optional[datetime] = Field(default=None, description="Data de criação do recibo")
    nome: Optional[str] = Field(default=None, description="Nome associado ao recibo")
    categoria: Optional[str] = Field(default=None, description="Categoria do recibo")
    vencimento_base: Optional[float] = Field(default=None, description="Valor base do vencimento")
    id_beneficiario: Optional[str] = Field(default=None, description="Identificador do beneficiário")
    vencimento_hora: Optional[float] = Field(default=None, description="Valor do vencimento por hora")
    departamento: Optional[str] = Field(default=None, description="Departamento associado")
    seguro: Optional[str] = Field(default=None, description="Tipo de seguro associado")
    data_fecho: Optional[datetime] = Field(default=None, description="Data de fecho do recibo")
    id_colaborador: Optional[str] = Field(default=None, description="Identificador do colaborador")
    valor_liquido: Optional[float] = Field(default=None, description="Valor líquido do recibo")
    valor_bruto: Optional[float] = Field(default=None, description="Valor bruto do recibo")
    descontos_totais: Optional[float] = Field(default=None, description="Total de descontos")
    dias_trabalhados: Optional[int] = Field(default=None, description="Número de dias trabalhados")
    nif: Optional[str] = Field(default=None, description="Número de identificação fiscal")
    ano: Optional[int] = Field(default=None, description="Ano do recibo")
    mes: Optional[int] = Field(default=None, description="Mês do recibo")
    data_alteracao: Optional[datetime] = Field(default=None, description="Data da última alteração")
    link: Optional[str] = Field(default=None, description="Link para o recibo")
    entidade_id: Optional[int] = Field(default=0, description="ID da entidade emissora do recibo")

    def dict(self, *args, **kwargs):
        original_dict = super().model_dump(*args, **kwargs)
        for key, value in original_dict.items():
            if isinstance(value, datetime):
                original_dict[key] = value.isoformat()
        return original_dict

    @classmethod
    def get_tabela(cls, result: List[Any]) -> pd.DataFrame:
        dados = []
        for s in result:
            dados.append(s.model_dump())
        df = pd.DataFrame(dados)
        return df
    