import re
import PyPDF2
from io import BytesIO
from datetime import datetime
from src.recibos.classRecibo import Recibo
from src.services.logger import setup_logger
logger = setup_logger()


tabela = 'recibos'
class ReciboExtractor:
    def __init__(self, pdf):
        self.texto, self.file = self.ler_pdf(pdf)

    def ler_pdf(self, pdf):
        file = None
        try:         
            if isinstance(pdf, BytesIO):
                origem = 'streamlit'
                reader = PyPDF2.PdfReader(pdf)
                texto = ''.join(page.extract_text() for page in reader.pages)


            elif isinstance(pdf, str):
                origem = 'local'
                with open(pdf, "rb") as pdf_file:
                    reader = PyPDF2.PdfReader(pdf_file)
                    texto = ''.join(page.extract_text() for page in reader.pages)    
                    file = pdf_file
                message = {
                    'origem': origem,
                    'texto': texto,
                }
            else:
                message = {
                    'erro': 'Tipo de arquivo não suportado!',
                }
                logger.error(f"Erro ao ler PDF", extra={"message": message}, ThreadName="ReciboExtractor.ler_pdf")
                raise ValueError("Tipo de arquivo não suportado!")
            

            if not texto:
                message = {
                    'erro': 'PDF VAZIO!',
                }
                logger.error(f"Erro ao ler PDF", extra={"message": message}, ThreadName="ReciboExtractor.ler_pdf")
                raise ValueError("PDF VAZIO!")          
            return texto, file
        
        except Exception as e:
            message = {
                'erro': e,
            }
            logger.error(f"Erro ao ler PDF", extra={"message": message}, ThreadName="ReciboExtractor.ler_pdf")
            raise ValueError(f"ERRO AO LER PDF: {e}")
        






    def extrair_nome(self):
        padrao = r'Original\n(.*?)\s+Nome'
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            message = {
                'match': match.group(0),
                'valor': value,
            }
        else:
            value = None
            message = {
                'erro': 'sem match',
            }
            logger.error(f"Erro ao extrair nome", extra={"message": message}, ThreadName="ReciboExtractor.extrair_nome")
        return value
    
    def extrair_id_beneficiario(self):
        padrao = r'Venc. / Hora\s+([\d,.]+)\s+([\d,.]+)'
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(2)
            message = {
                'match': match.group(0),
                'valor': value,
            }
        else:
            value = None
            message = {
                'erro': 'sem match',
            }
            logger.error(f"Erro ao extrair id beneficiario", extra={"message": message}, ThreadName="ReciboExtractor.extrair_id_beneficiario")
        return value

    def extrair_dias_trabalhados(self):
        padrao = r'N.\s+Dias\s+Mês:\s+([\d,.]+)'
        match = re.search(padrao, self.texto)
        if match:
            value = int(float(match.group(1)))
            message = {
                'valor': value,
                'match': match.group(0),
            }
        else:
            value = None
            message = {
                'erro': 'sem match',
            }
            logger.error(f"Erro ao extrair dias trabalhados", extra={"message": message}, ThreadName="ReciboExtractor.extrair_dias_trabalhados")
        return value
    
    def extrair_nif(self):
        padrao = r'Contrib.\s+N.\s+Dias\s+Mês:\s+[\d,.]+\s+([\d,.]+)'
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            message = {
                'valor': value,
                'match': match.group(0),
            }
        else:
            value = None
            message = {
                'erro': 'sem match',
            }
            logger.error(f"Erro ao extrair nif", extra={"message": message}, ThreadName="ReciboExtractor.extrair_nif")
        return value
    
    def extrair_seguro(self):
        padrao = r'(.+)\s+Seguro'
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            message = {
                'valor': value,
                'match': match.group(0),
            }
        else:
            value = None
            message = {
                'erro': 'sem match',
            }
            logger.error(f"Erro ao extrair seguro", extra={"message": message}, ThreadName="ReciboExtractor.extrair_seguro")
        return value

    def extrair_data_fecho(self) -> datetime:
        padrao = r'Data Fecho (\d{2}[-/]\d{2}[-/]\d{4})'
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            value = value.replace('-', '/')
            value = datetime.strptime(value, '%d/%m/%Y')
            message = {
                'valor': str(value),
                'match': match.group(0),
            }
        else:
            value = datetime(1900, 1, 1, 0, 0, 0)
            message = {
                'erro': 'sem match',
            }
            logger.error(f"Erro ao extrair data fecho", extra={"message": message}, ThreadName="ReciboExtractor.extrair_data_fecho")
        return value
    
    def extrair_id_colaborador(self):
        padrao = r'N.º Mecan.\D*(\d+)'
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            message = {
                'valor': value,
                'match': match.group(0),
            }
        else:
            value = None
            message = {
                'erro': 'sem match',
            }
            logger.error(f"Erro ao extrair id colaborador", extra={"message": message}, ThreadName="ReciboExtractor.extrair_id_colaborador")
        return value

    def extrair_valor_liquido(self):
        # padrao = r'(\d{1,3}(?:\.\d{3})*,\d{2})\s*Total Pago \( EUR \)'
        padrao = r'(\d{1,3}(?:[ .]\d{3})*,\d{2})\s*Total Pago \( EUR \)'
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            value = float(value.replace(' ', '').replace('.', '').replace(',', '.'))
            message = {
                'valor': value,
                'match': match.group(0),
            }
        else:
            value = 0.0
            message = {
                'erro': 'sem match',
            }
            logger.error(f"Erro ao extrair valor liquido", extra={"message": message}, ThreadName="ReciboExtractor.extrair_valor_liquido")
        return value

    def extrair_valor_bruto(self):
        padrao = r'Total\s+\d{1,3}(?:[.,]\d{2})\s+([\d\s.,]+)\n'
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            value = value.replace(' ', '')
            value = value.replace('.', '')
            value = value.replace(',', '.')
            value = float(value)
            message = {
                'valor': value,
                'match': match.group(0), 
            }
        else:
            value = 0.0
            message = {
                'erro': 'sem match',
            }
            logger.error(f"Erro ao extrair valor bruto", extra={"message": message}, ThreadName="ReciboExtractor.extrair_valor_bruto")
        return value

    def extrair_descontos_totais(self):
        padrao = r'Total\s+([\d\s,.]+)\s+[\d\s,.]+\n'
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            value = float(value.replace(' ', '').replace('.', '').replace(',', '.'))
            value = round(value, 2)
            message = {
                'valor': value,
                'match': match.group(0),
            }
        else:
            value = 0.0
            message = {
                'erro': 'sem match',
            }
            logger.error(f"Erro ao extrair descontos totais", extra={"message": message}, ThreadName="ReciboExtractor.extrair_descontos_totais")
        return value

    def extrair_categoria(self):
        padrao = r'Categoria Vencimento\s+(\S+)\s+(.*)'
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(2)
            value = re.sub(r'[^a-zA-ZçáéíóúãõâêôÇÁÉÍÓÚÃÕÂÊÔ\s]', '', value).strip()
            message = {
                'valor': value,
                'match': match.group(0),
            }
        else:
            value = None
            message = {
                'erro': 'sem match',
            }
            logger.error(f"Erro ao extrair categoria", extra={"message": message}, ThreadName="ReciboExtractor.extrair_categoria")
        return value
    
    def extrair_departamento(self):
        padrao = r'Departamento\s+(.+)'
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            message = {
                'valor': value,
                'match': match.group(0),
            }
        else:
            value = None
            message = {
                'erro': 'sem match',
            }
            logger.error(f"Erro ao extrair departamento", extra={"message": message}, ThreadName="ReciboExtractor.extrair_departamento")
        return value

    def extrair_vencimento_hora(self):
        padrao = r'Venc. / Hora\s+([\d,.]+)\s+([\d,.]+)'
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            value = value.replace(' ', '')
            value = value.replace('.', '')
            value = value.replace(',', '.')
            value = float(value)
            message = {
                'valor': value,
                'match': match.group(0),
            }
        else:
            value = 0.0
            message = {
                'erro': 'sem match',
            }
            logger.error(f"Erro ao extrair vencimento hora", extra={"message": message}, ThreadName="ReciboExtractor.extrair_vencimento_hora")
        return value

    def extrair_vencimento_base(self):
        padrao = r'Categoria Vencimento\s+([\d\s,.]+)\s+'
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            value = float(value.replace(' ', '').replace('.', '').replace(',', '.'))
            message = {
                'valor': value,
                'match': match.group(0),
            }
        else:
            value = 0.0
            message = {
                'erro': 'sem match',
            }
            logger.error(f"Erro ao extrair vencimento base", extra={"message": message}, ThreadName="ReciboExtractor.extrair_vencimento_base")
        return value
    
    def run_extraction(self):
        nome = self.extrair_nome()
        id_beneficiario = self.extrair_id_beneficiario()
        nif = self.extrair_nif()
        dias_trabalhados = self.extrair_dias_trabalhados()
        seguro = self.extrair_seguro()
        data_fecho = self.extrair_data_fecho()
        id_colaborador = self.extrair_id_colaborador()
        valor_liquido = self.extrair_valor_liquido()
        valor_bruto = self.extrair_valor_bruto()
        descontos_totais = self.extrair_descontos_totais()
        categoria = self.extrair_categoria()
        departamento = self.extrair_departamento()
        vencimento_hora = self.extrair_vencimento_hora()
        vencimento_base = self.extrair_vencimento_base()
        ano = data_fecho.year if data_fecho else None
        mes = data_fecho.month if data_fecho else None
        entidade_id = 10 # empifarma

        recibo = Recibo(
            nome=nome,
            id_beneficiario=id_beneficiario,
            dias_trabalhados=dias_trabalhados,
            nif=nif,
            seguro=seguro,
            data_fecho=data_fecho,
            id_colaborador=id_colaborador,
            valor_liquido=valor_liquido,
            valor_bruto=valor_bruto,
            descontos_totais=descontos_totais,
            categoria=categoria,
            departamento=departamento,
            vencimento_hora=vencimento_hora,
            vencimento_base=vencimento_base, 
            ano=ano,
            mes=mes,
            entidade_id=entidade_id
        ).dict(exclude_unset=True, exclude_none=True)
        logger.info(msg="", Table='recibos', ThreadName="ReciboExtractor", **recibo)
        return recibo