from sqlmodel import SQLModel, Field, Relationship
from typing import Optional, List
from datetime import datetime
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy import Column, text, Boolean, DateTime, String
from uuid import UUID


class Conta(SQLModel, table=True):
    __tablename__ = "contas"
    id: int = Field(primary_key=True, sa_column_kwargs={"autoincrement": True})
    id_chave: UUID = Field(sa_column=Column(PG_UUID(as_uuid=True), nullable=False, unique=True, server_default=text("gen_random_uuid()")))
    conta: str = Field(sa_column=Column(type_=String, nullable=True, unique=True))
    utilizador: str = Field(sa_column=Column(type_=String, nullable=True))
    entidade_id: int = Field(foreign_key="entidades.id")
    ativo: bool = Field(sa_column=Column(<PERSON>olean, nullable=False, server_default=text("true")))
    data_criacao: datetime = Field(sa_column=Column(type_=DateTime, nullable=False, server_default=text("now()")))
    data_alteracao: datetime = Field(sa_column=Column(type_=DateTime, nullable=False, server_default=text("now()")))

    entidade_rel: Optional["Entidade"] = Relationship(back_populates="contas_rel") # type: ignore
    comprovativos_rel: List["Comprovativo"] = Relationship(back_populates="conta_rel") # type: ignore

class ContaCreate(SQLModel):
    conta: str
    utilizador: str
    entidade_id: int
    ativo: Optional[bool] = Field(default=True)

    class Config:
        json_schema_extra = {
            "example": {
                "conta": "teste_conta",
                "utilizador": "teste_utilizador",
                "entidade_id": 1,
                "ativo": True
            }
        }


class ContaUpdate(SQLModel):
    conta: Optional[str] = Field(default=None)
    utilizador: Optional[str] = Field(default=None)
    entidade_id: Optional[int] = Field(default=None)
    ativo: Optional[bool] = Field(default=None)


class ContaBase(SQLModel):
    id: int = Field(default=None)
    id_chave: UUID = Field(default=None)
    conta: str = Field(default=None)
    utilizador: str = Field(default=None)
    entidade_id: Optional[int] = Field(default=None)
    ativo: bool = Field(default=None)
    data_criacao: datetime = Field(default=None)
    data_alteracao: datetime = Field(default=None)
