from datetime import datetime
from typing import Any, List, Optional
from uuid import UUID
import pandas as pd
from pydantic import BaseModel, Field



class Comprovativo(BaseModel):
    id: Optional[int] = Field(default=None)
    id_chave: Optional[UUID] = Field(default=None)
    data_valor: Optional[datetime] = Field(default=None)
    data_movimento: Optional[datetime] = Field(default=None)
    movimento: Optional[str] = Field(default=None)
    valor: Optional[float] = Field(default=None)
    tipo_movimento: Optional[str] = Field(default=None)
    entidade: Optional[str] = Field(default=None)
    numero_cartao: Optional[str] = Field(default=None)
    tipo_pagamento: Optional[str] = Field(default=None)
    id_sibs: Optional[str] = Field(default=None)
    ordenante: Optional[str] = Field(default=None)
    conta_destino: Optional[str] = Field(default=None)
    origem_operacao: Optional[str] = Field(default=None)
    id_transferencia: Optional[str] = Field(default=None)
    local: Optional[str] = Field(default=None)
    swift_destinatario: Optional[str] = Field(default=None)
    iban_destinatario: Optional[str] = Field(default=None)
    motivo_sepa: Optional[str] = Field(default=None)
    observacao: Optional[str] = Field(default=None)
    ano: Optional[int] = Field(default=None)
    mes: Optional[int] = Field(default=None)
    link: Optional[str] = Field(default=None)
    cont_trf: Optional[str] = Field(default=None)
    conta_id: Optional[int] = Field(default=0)
    segmento_id: Optional[int] = Field(default=0)
    entidade_id: Optional[int] = Field(default=0)
    ativo: Optional[bool] = Field(default=True)
    data_criacao: Optional[datetime] = Field(default=None)
    data_alteracao: Optional[datetime] = Field(default=None)

    def dict(self, *args, **kwargs) -> dict:
        original_dict = super().model_dump(*args, **kwargs)
        for key, value in original_dict.items():
            if isinstance(value, datetime):
                original_dict[key] = value.isoformat()
        return original_dict

    @classmethod
    def get_tabela(cls, result: List[Any]) -> pd.DataFrame:
        dados = [s.model_dump(exclude_unset=True) for s in result]
        return pd.DataFrame(dados)
    



class ComprovativoPadrao(BaseModel):
    id: Optional[int] = Field(default=None)
    id_chave: Optional[UUID] = Field(default=None)
    padrao: Optional[str] = Field(default=None)
    segmento_id: Optional[int] = Field(default=None)
    entidade_id: Optional[int] = Field(default=None)
    ativo: Optional[bool] = Field(default=True)
    data_criacao: Optional[datetime] = Field(default=None)
    data_alteracao: Optional[datetime] = Field(default=None)
