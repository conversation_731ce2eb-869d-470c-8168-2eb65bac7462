from typing import List
from sqlmodel import Session
from fastapi import APIRouter, Depends
from comum.models.estados import EstadoBase, EstadoCreate, EstadoUpdate
from app.database.postgres import get_session
from app.workers.estados import EstadosWorker

estados_router = APIRouter()

def get_worker(session: Session = Depends(get_session)) -> EstadosWorker:
    return EstadosWorker(session)


@estados_router.get("/estados", response_model=List[EstadoBase])
async def lista_completa(worker: EstadosWorker = Depends(get_worker)):
    return list(worker.lista())


@estados_router.get("/estados/{id}", response_model=EstadoBase)
async def procura(id: int, worker: EstadosWorker = Depends(get_worker)):
    return worker.procura(id)


@estados_router.post("/estados", response_model=EstadoBase)
async def cria(novos_dados: EstadoCreate, worker: EstadosWorker = Depends(get_worker)):
    return worker.cria(novos_dados)


@estados_router.patch("/estados/{id}", response_model=EstadoBase)
async def atualiza(id: int, dados: EstadoUpdate, worker: EstadosWorker = Depends(get_worker)):
    return worker.atualiza(id, dados)
    

@estados_router.delete("/estados/{id}")
async def remove(id: int, worker: EstadosWorker = Depends(get_worker)):
    worker.remove(id)
    return {"message": f"ID: **{id}** deleted!"}