from sqlmodel import SQLModel, Field
from typing import Optional
from datetime import datetime
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy import Column, text, Boolean, DateTime, String
from uuid import UUID


class ReciboCodigo(SQLModel, table=True):
    __tablename__ = "recibos_codigos"
    id: int = Field(primary_key=True, sa_column_kwargs={"autoincrement": True})
    id_chave: UUID = Field(sa_column=Column(PG_UUID(as_uuid=True), nullable=False, unique=True, server_default=text("gen_random_uuid()")))
    codigo: str = Field(sa_column=Column(type_=String, nullable=False))
    descricao: str = Field(sa_column=Column(type_=String, nullable=True))
    ativo: bool = Field(sa_column=Column(Boolean, nullable=False, server_default=text("true")))
    data_criacao: datetime = Field(sa_column=Column(type_=DateTime, nullable=False, server_default=text("now()")))
    data_alteracao: datetime = Field(sa_column=Column(type_=DateTime, nullable=False, server_default=text("now()")))


class ReciboCodigoCreate(SQLModel):
    codigo: str
    descricao: str
    ativo: Optional[bool] = Field(default=True)

    class Config:
        json_schema_extra = {
            "example": {
                "codigo": "teste_codigo",
                "descricao": "teste_descricao",
                "ativo": True
            }
        }


class ReciboCodigoUpdate(SQLModel):
    codigo: Optional[str] = Field(default=None)
    descricao: Optional[str] = Field(default=None)
    ativo: Optional[bool] = Field(default=None)


class ReciboCodigoBase(SQLModel):
    id: int = Field(default=None)
    id_chave: UUID = Field(default=None)
    codigo: str = Field(default=None)
    descricao: str = Field(default=None)
    ativo: bool = Field(default=None)
    data_criacao: datetime = Field(default=None)
    data_alteracao: datetime = Field(default=None)
