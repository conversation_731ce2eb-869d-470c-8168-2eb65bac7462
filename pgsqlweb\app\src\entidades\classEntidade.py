from datetime import datetime
from pydantic import BaseModel, Field
from src.services.supabase_client import connect



class Entidade(BaseModel):
    id_chave: int = Field(..., description="Chave primária, gerada automaticamente.")
    entidade: str = Field(..., description="Padrão associado ao segmento.")
    data_criacao: datetime = Field(default_factory=lambda: datetime.now(), description="Data de criação do registro, com fuso UTC.")
    data_alteracao: datetime = Field(default_factory=lambda: datetime.now())

    class Config:
        from_attributes = True
        populate_by_name = True
        json_schema_extra = {
            "example": {
                "id_chave": 1,
                "entidade": "lidl",
                "data_criacao": "2024-12-05T12:00:00Z",
                "data_alteracao": "2024-12-05T12:00:00Z"
            }
        }

    @classmethod
    def get_entidades(cls):
        supabase = connect()
        response = supabase.table("entidades").select("*").order("id_chave", desc=False).execute()
        
        if not response.data:
            return []
        
        return [cls(**entidade) for entidade in response.data]