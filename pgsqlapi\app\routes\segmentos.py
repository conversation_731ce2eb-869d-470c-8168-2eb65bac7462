from typing import List
from fastapi import APIRouter, Depends
from sqlmodel import Session
from comum.models.segmentos import SegmentoBase, SegmentoCreate, SegmentoUpdate
from app.database.postgres import get_session
from app.workers.segmentos import SegmentosWorker

segmentos_router = APIRouter()

def get_worker(session: Session = Depends(get_session)) -> SegmentosWorker:
    return SegmentosWorker(session)


@segmentos_router.get("/segmentos", response_model=List[SegmentoBase])
async def lista_completa(worker: SegmentosWorker = Depends(get_worker)):
    return list(worker.lista())


@segmentos_router.get("/segmentos/{id}", response_model=SegmentoBase)
async def procura(id: int, worker: SegmentosWorker = Depends(get_worker)):
    return worker.procura(id)


@segmentos_router.post("/segmentos", response_model=SegmentoBase)
async def cria(novos_dados: SegmentoCreate, worker: SegmentosWorker = Depends(get_worker)):
    return worker.cria(novos_dados)


@segmentos_router.patch("/segmentos/{id}", response_model=SegmentoBase)
async def atualiza(id: int, dados: SegmentoUpdate, worker: SegmentosWorker = Depends(get_worker)):
    return worker.atualiza(id, dados)
    

@segmentos_router.delete("/segmentos/{id}")
async def remove(id: int, worker: SegmentosWorker = Depends(get_worker)):
    worker.remove(id)
    return {"message": f"ID: **{id}** deleted!"}