from datetime import datetime
from typing import Any, List, Optional
import pandas as pd
from pydantic import BaseModel
from uuid import UUID


class Segmento(BaseModel):
    id: Optional[int] = None
    id_chave: Optional[UUID] = None
    segmento : Optional[str] = None
    categoria : Optional[str] = None
    icon : Optional[str] = None
    ativo: Optional[bool] = None
    data_criacao: Optional[datetime] = None
    data_alteracao : Optional[datetime] = None

    def dict(self, *args, **kwargs):
        original_dict = super().model_dump(*args, **kwargs)
        for key, value in original_dict.items():
            if isinstance(value, datetime):
                original_dict[key] = value.isoformat()
        return original_dict

    @classmethod
    def criar_df(cls, result: List[Any]) -> pd.DataFrame:
        dados = []
        for s in result:
            dados.append(s.model_dump(exclude_unset=True, exclude_none=True))
        df = pd.DataFrame(dados)
        return df
    