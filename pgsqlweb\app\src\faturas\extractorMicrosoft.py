import re
from datetime import datetime
from src.services.log import Logger, get_funcao
from src.services.file_service import FileService
from src.faturas.classFatura import Fat<PERSON>, Cabecal<PERSON>, Linhas

logger = Logger()

tabela = 'microsoft'

class FaturaMicrosoftExtractor:
    def __init__(self, pdf):
        self.file = FileService(file=pdf)
        self.texto = self.file.extrair_texto_pdfplumber()
    
    
    def extrair_data(self) -> datetime:
        funcao = get_funcao()
        padroes = [
            (r'\b\d{2}-\d{2}-\d{4}\b', '%d-%m-%Y'),
            (r'\b\d{2}/\d{2}/\d{4}\b', '%d/%m/%Y')
        ]
        match = None
        value = datetime(1900, 1, 1, 0, 0, 0)  # Valor padrão se nenhum padrão for encontrado
        
        for padrao, formato in padroes:
            match = re.search(padrao, self.texto)
            if match:
                date_str = match.group(0).replace('-', '/')
                value = datetime.strptime(date_str, formato.replace('-', '/'))
                break

        message = {
            'valor': value,
            'match': match
        }
        logger.log_info(message=message, tabela=tabela, funcao=funcao)
        return value
    
    
    def extrair_fatura(self) -> str | None:
        funcao = get_funcao()
        value = None
        match = None
        padrao1 = r'Invoice Number: (.*)'
        padrao2 = r'N.º de Fatura:\s*([\w\s/]+?)(?=\sData)' 
        padrao3 = r"(FT\s\w{2}\d{2}/\d{8})"
        match1 = re.search(padrao1, self.texto)
        match2 = re.search(padrao2, self.texto)
        match3 = re.search(padrao3, self.texto)
        if match1:
            value = match1.group(1)
            match = match1
        if match2:
            value = match2.group(1)
            match = match2
        if match3:
            value = match3.group(1)
            match = match3
        message = {
            'valor': value,
            'match': match
        }
        logger.log_info(message=message, tabela=tabela, funcao=funcao)
        return value

    def extrair_valor(self) -> float:
        funcao = get_funcao()
        match = None
        padrao1 = r'Total Amount: ([\d,.]+) EUR'
        padrao2 = r'Montante Total \(EUR\)\s*([\d,.]+)'
        match1 = re.search(padrao1, self.texto)
        match2 = re.search(padrao2, self.texto)
        if match1:
            match = match1
            value = match1.group(1)
            value = float(value.replace(' ', '').replace(',', '.'))
        if match2:
            match = match2
            value = match2.group(1)
            value = float(value.replace(' ', '').replace(',', '.'))
        if not match1 and not match2:
            match = 'padrao nao encontrado'
            value = 0.00
        message = {
            'valor': value,
            'match': match
        }
        logger.log_info(message=message, tabela=tabela, funcao=funcao)
        return value
    
    def extrair_iva_23(self) -> float:
        funcao = get_funcao()
        value = 0.00
        match = None
        padrao1 = r'Total VAT \(23,00%\):\s*([\d,.]+)'
        padrao2 = r'IVA \(\d+,\d+%\) (\d+,\d+)'
        match1 = re.search(padrao1, self.texto)
        match2 = re.search(padrao2, self.texto)
        if match1:
            match = match1
            value = match1.group(1)
            value = float(value.replace(' ', '').replace(',', '.'))
        if match2:
            match = match2
            value = match2.group(1)
            value = float(value.replace(' ', '').replace(',', '.'))


        message = {
            'valor': value,
            'match': match
        }
        logger.log_info(message=message, tabela=tabela, funcao=funcao)



        return value
    
    def extrair_iva_06(self) -> float:
        funcao = get_funcao()
        message = {
            'valor': 'sem padrao configurado', 
            'match': 'sem padrao configurado'
            }
        logger.log_info(message=message, tabela=tabela, funcao=funcao)
        return 0.00

    
    

    
    def extrair_linhas(self):
        value = None
        funcao = get_funcao()
        linhas_texto = self.texto.split('\n')
        linhas = []
        # padrao1 = r"([\w\s]+)\s+(\d+,\d+)\s+\d+,\d+\s+(\d+,\d+)\s+\(\d+,\d+%\)\s+(\d+,\d+)"
        # padrao2 = r"([^\d]+?)\s*([\+\-]?\d+)\s+(\d+)\s+(.+?)\s+(\d+,\d+)"
        # padrao3 = r"([\w\s]+)\s+(\d+,\d+)\s+(\d+,\d+)\s+(.+?)\s+(\d+,\d+)"
        padrao2020 = r"(?P<artigo>[\w\s\+\-]+) \d+ Total de Custos Antes do Imposto (?P<base>\d+,\d+)"
        padrao2021 = r"(?P<artigo>[\w\s]+) (?P<base>\d+,\d+)"
        padroes = [padrao2020, padrao2021]
        
        
        for linha in linhas_texto:
            for padrao in padroes:
                match = re.match(padrao, linha)
                if match:
                    artigo = match.group('artigo').strip()
                    base = float(match.group('base').replace(',', '.'))
                    desconto = 0.00
                    tx_iva = 0.23
                    iva = round(base * tx_iva, 2)
                    valor = base + iva
                    valor_bruto = base + desconto
                    linhas.append(Linhas(
                        artigo=artigo, 
                        base=base,
                        desconto=desconto,
                        valor_bruto=valor_bruto,
                        valor=valor,
                        iva=iva,
                        tx_iva=tx_iva,

                        ))
                    message = { 
                                'padrao': 'match2',
                                'match': match
                                }
                    logger.log_info(message=message, tabela=tabela, funcao=funcao) 
                    break               
            

        return linhas






    def run_extraction(self) -> Fatura:
        data = self.extrair_data()
        fatura = self.extrair_fatura()
        valor = self.extrair_valor()
        iva_23 = self.extrair_iva_23()
        iva_06 = self.extrair_iva_06()
        iva = iva_06 + iva_23
        desconto = 0.00
        base = valor - iva
        valor_bruto = base + desconto
        entidade_id = 19
        ano = data.year
        mes = data.month

        cabecalho = Cabecalho(
                                data=data,
                                fatura=fatura,
                                valor=valor,
                                iva=iva,
                                desconto=desconto,
                                base=base, 
                                valor_bruto=valor_bruto,
                                entidade_id=entidade_id,
                                ano=ano,
                                mes=mes
                            )



        linhas_extraidas = self.extrair_linhas()

        for linha in linhas_extraidas:
            linha.data = cabecalho.data
            linha.fatura = cabecalho.fatura
            linha.ano = linha.data.year if linha.data else None
            linha.mes = linha.data.month if linha.data else None
        fatura = Fatura(cabecalho=cabecalho, linhas=linhas_extraidas)
        return fatura
    


