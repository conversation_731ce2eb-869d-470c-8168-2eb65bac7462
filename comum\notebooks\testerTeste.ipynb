{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from src.teste.classTeste import Teste\n", "\n", "teste = Teste(campo1='valor1', campo2='valor2', campo3='valor3')\n", "result = Teste.insert_teste(teste)\n", "result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from src.services.supabase_client import connect\n", "import time\n", "import threading\n", "supabase = connect()\n", "\n", "def processa_evento(evento):\n", "    try:\n", "        if evento['tipo'] == 'INSERT' and evento['tabela'] == 'teste':\n", "            supabase.rpc(\"avalia_teste_insert\", {\"id_evento\": evento['id_chave'],\"fkid_chave\": evento['fkid_chave']}).execute()\n", "    except Exception as e:\n", "        supabase.table(\"eventos\").update({\"estado\": 99}).eq(\"id_chave\", evento['id_chave']).execute()\n", "        ev = 'processa_evento' + str(evento['id_chave']) \n", "        print(e)\n", "        # LogApp.log_error(classe='cron_job', evento=ev, resultado=str(e))\n", "\n", "\n", "def processar_eventos():\n", "    while True:\n", "        try:\n", "            eventos = supabase.table(\"eventos\").select(\"*\").eq(\"estado\", 0).order('data_criacao', desc=False).execute()\n", "            for evento in eventos.data:\n", "                print(evento['id_chave'])\n", "        except Exception as e:\n", "            # LogApp.log_error(classe='cron_job', evento='processar_eventos', resultado=str(e))\n", "            print(e)\n", "        \n", "        time.sleep(10)\n", "\n", "processar_eventos()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from src.services.supabase_client import connect\n", "supabase = connect()\n", "\n", "eventos = supabase.table(\"eventos\").select(\"*\").eq(\"estado\", 0).execute()\n", "evento = eventos.data[0]\n", "id_evento = evento['id_chave']\n", "fkid_chave = evento['fkid_chave']\n", "response = supabase.rpc(\"avalia_teste_insert\", {\"id_evento\": id_evento,\"fkid_chave\": fkid_chave}).execute()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["data=[{'id_chave': 15, 'data_criacao': '2025-04-02T22:00:29.95188+00:00', 'data_alteracao': '2025-04-02T22:00:29.95188+00:00', 'contacto_id': 'd3ad9298-adac-4eb1-9fda-ccd916a7311a', 'telefone': '999999999', 'tipo': 'mobile', 'cont_trf': '999999'}] count=None\n", "{'contacto_id': 'd3ad9298-adac-4eb1-9fda-ccd916a7311a', 'telefone': '999999999', 'tipo': 'mobile', 'cont_trf': '999999'}\n", "data=[{'id_chave': 16, 'data_criacao': '2025-04-02T22:00:30.117417+00:00', 'data_alteracao': '2025-04-02T22:00:30.117417+00:00', 'contacto_id': 'd3ad9298-adac-4eb1-9fda-ccd916a7311a', 'telefone': '888888888', 'tipo': 'home', 'cont_trf': '888888'}] count=None\n", "{'contacto_id': 'd3ad9298-adac-4eb1-9fda-ccd916a7311a', 'telefone': '888888888', 'tipo': 'home', 'cont_trf': '888888'}\n"]}], "source": ["from src.contactos.workerContacto import WorkerContacto\n", "from src.services.supabase_client import connect\n", "from src.contactos.classContacto import ContactoTelefone\n", "\n", "supabase = connect()\n", "\n", "\n", "contacto = WorkerContacto().get_contacto('d3ad9298-adac-4eb1-9fda-ccd916a7311a')\n", "if contacto.origem == '<EMAIL>':\n", "    for telefone in contacto.telefones:\n", "        last9 = telefone.field[-9:]\n", "        cont_trf = last9[:3] + last9[-3:]\n", "        contactotelefone = ContactoTelefone(\n", "                                                contacto_id=contacto.id_chave,\n", "                                                telefone=telefone.field,\n", "                                                tipo=telefone.label,\n", "                                                cont_trf=cont_trf\n", "                                            )\n", "\n", "        dados = contactotelefone.model_dump(exclude_unset=True, exclude_none=True, mode='json')\n", "        response = supabase.table(\"contactos_telefones\").insert(dados).execute()\n", "        print(response)\n", "        print(dados)"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.0"}}, "nbformat": 4, "nbformat_minor": 2}