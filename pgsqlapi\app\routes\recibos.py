from typing import List
from sqlmodel import Session
from fastapi import APIRouter, Depends
from comum.models.recibos import ReciboBase, ReciboCreate, ReciboUpdate
from app.database.postgres import get_session
from app.workers.recibos import RecibosWorker


recibos_router = APIRouter()

def get_worker(session: Session = Depends(get_session)) -> RecibosWorker:
    return RecibosWorker(session)


@recibos_router.get("/recibos", response_model=List[ReciboBase])
async def lista_completa(worker: RecibosWorker = Depends(get_worker)):
    return list(worker.lista())


@recibos_router.get("/recibos/{id}", response_model=ReciboBase)
async def procura(id: int, worker: RecibosWorker = Depends(get_worker)):
    return worker.procura(id)


@recibos_router.post("/recibos", response_model=ReciboBase)
async def cria(novos_dados: ReciboCreate, worker: RecibosWorker = Depends(get_worker)):
    return worker.cria(novos_dados)


@recibos_router.patch("/recibos/{id}", response_model=ReciboBase)
async def atualiza(id: int, dados: ReciboUpdate, worker: RecibosWorker = Depends(get_worker)):
    return worker.atualiza(id, dados)
    

@recibos_router.delete("/recibos/{id}")
async def remove(id: int, worker: RecibosWorker = Depends(get_worker)):
    worker.remove(id)
    return {"message": f"ID: **{id}** deleted!"}