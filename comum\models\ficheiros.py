from sqlmodel import SQLModel, Field, Relationship
from typing import Optional
from datetime import datetime
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy import Column, text, Boolean, DateTime, String, Text
from uuid import UUID


class Ficheiro(SQLModel, table=True):
    __tablename__ = "ficheiros"
    id: int = Field(primary_key=True, sa_column_kwargs={"autoincrement": True})
    id_chave: UUID = Field(sa_column=Column(PG_UUID(as_uuid=True), nullable=False, unique=True, server_default=text("gen_random_uuid()")))
    tabela: str = Field(sa_column=Column(type_=String, nullable=False))
    fkid_chave: UUID = Field(sa_column=Column(PG_UUID(as_uuid=True), nullable=False))
    binario: str = Field(sa_column=Column(Text, nullable=False))
    data_criacao: datetime = Field(sa_column=Column(type_=DateTime, nullable=False, server_default=text("now()")))
    data_alteracao: datetime = Field(sa_column=Column(type_=DateTime, nullable=False, server_default=text("now()")))
    ativo: bool = Field(sa_column=Column(Boolean, nullable=False, server_default=text("true")))


class FicheiroCreate(SQLModel):
    tabela: str
    fkid_chave: UUID
    binario: str
    ativo: Optional[bool] = Field(default=True)

    class Config:
        json_schema_extra = {
            "example": {
                "tabela": "teste_tabela",
                "fkid_chave": "teste_fkid_chave",
                "binario": "teste_dados_binarios",
                "ativo": True
            }
        }


class FicheiroUpdate(SQLModel):
    tabela: Optional[str] = Field(default=None)
    fkid_chave: Optional[UUID] = Field(default=None)
    binario: Optional[str] = Field(default=None)
    ativo: Optional[bool] = Field(default=None)


class FicheiroBase(SQLModel):
    id: int = Field(default=None)
    id_chave: UUID = Field(default=None)
    tabela: str = Field(default=None)
    fkid_chave: UUID = Field(default=None)
    binario: str = Field(default=None)
    ativo: bool = Field(default=None)
    data_criacao: datetime = Field(default=None)
    data_alteracao: datetime = Field(default=None)
