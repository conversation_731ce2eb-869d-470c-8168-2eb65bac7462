import streamlit as st
import pandas as pd
from src.services.api import api_get



def get_recibos():
    response = api_get(tabela='recibos')
    df = pd.DataFrame([r for r in response.json()])
    return df


def main():


    df_recibos = get_recibos()
    df_recibos = df_recibos[df_recibos['valor_liquido'] > 100] # REMOVE OS RECIBOS DE PREMIOS 
    df_recibos = df_recibos.drop(columns=['id_chave', 'data_criacao', 'data_alteracao', 'id_beneficiario', 'nome'])
    anos = df_recibos['ano'].unique()
    anos.sort()

    with st.sidebar:
        selected_ano = st.pills("Ano", anos, selection_mode="multi")
        if selected_ano:
            df_recibos = df_recibos[df_recibos['ano'].isin(selected_ano)]


    total_bruto = round(df_recibos['valor_bruto'].sum() ,2)
    total_descontos = round(df_recibos['descontos_totais'].sum() ,2)
    total_liquido = round(df_recibos['valor_liquido'].sum() ,2)
    total_dias = round(df_recibos['dias_trabalhados'].sum() ,2)

    avg_base = round(df_recibos['vencimento_base'].mean() ,2)
    avg_hora = round(df_recibos['vencimento_hora'].mean() ,2)
    avg_bruto = round(df_recibos['valor_bruto'].mean() ,2)
    avg_descontos = round(df_recibos['descontos_totais'].mean() ,2)
    avg_liquido = round(df_recibos['valor_liquido'].mean() ,2)
    avg_dias = round(df_recibos['dias_trabalhados'].mean() ,0)



    col1, col2, col3, col4 , col5= st.columns(5)
    with col1:
        st.metric("Total Dias", total_dias, border=True)
        st.metric("Média Dias", avg_dias, border=True)
    
    with col2:
        st.metric("Média Base", avg_base, border=True)
        st.metric("Média Hora", avg_hora, border=True)
    
    with col3:
        st.metric("Total Bruto", total_bruto, border=True)
        st.metric("Média Bruto", avg_bruto, border=True)

    with col4:
        st.metric("Total Desconto", total_descontos, border=True)
        st.metric("Média Desconto", avg_descontos, border=True)

    with col5:
        st.metric("Total Liquido", total_liquido, border=True)
        st.metric("Média Liquido", avg_liquido, border=True)

    

    liquido_group = df_recibos.groupby('mes')['valor_liquido'].sum().reset_index()
    descontos_group = df_recibos.groupby('mes')['descontos_totais'].sum().reset_index()
    join_group = pd.merge(liquido_group, descontos_group, on='mes')

    st.line_chart(join_group.set_index('mes'), color=['#581010','#10581F'])

    st.dataframe(df_recibos)

if __name__ == "__main__":
    main()