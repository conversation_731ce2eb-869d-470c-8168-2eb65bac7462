import logging
import seqlog

def setup_logger():

    seqlog.log_to_seq(
        server_url="http://192.168.1.77:5341",
        # api_key="7gnEAaGAC9zWaYe6lKNh",
        api_key="OLwsDU1Qb1Alf6cBf9Fv",
        level=logging.INFO,
        batch_size=10,
        auto_flush_timeout=10,
        override_root_logger=False, 
        support_extra_properties=True,
    )
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logger = logging.getLogger(__name__)

    return logger