import re
from typing import Optional
from datetime import datetime, date
from comum.models.faturas_cabec import FaturaCabecCreate
from comum.models.faturas_linhas import FaturaLinhasCreate
from comum.services.logger import setup_logger
logger = setup_logger()

class FaturaLidlExtractor:
    def __init__(self, texto, nome_ficheiro):
        self.texto = texto
        self.nome_ficheiro = nome_ficheiro
        self.cabec = FaturaCabecCreate()
        self.linhas = [FaturaLinhasCreate()]

   
    def extrair_data(self) -> datetime:
        padrao = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})'
        match = re.search(padrao, self.texto)
        
        if match:
            value = match.group(1)
            value = value.replace('-', '/')
            value = datetime.strptime(value, '%Y/%m/%d %H:%M:%S')
        else:
            value = datetime(1900, 1, 1, 0, 0, 0)
            
            
        message = {
            'valor': value,
            'match': match
        }
        return value
    
    def extrair_nif(self) -> Optional[str]:
        padrao = r'(Contribuinte....: |NIF...: )(\d+)'
        match = re.search(padrao, self.texto) 
        if match:
            value = match.group(2)
        else:
            value = None
            
            
        message = {
            'valor': value,
            'match': match
        }
        return value
    
    def extrair_nif_entidade(self) -> Optional[str]:
        padrao = r'(?<=NIF:)\d+'
        match = re.search(padrao, self.texto) 
        if match:
            value = match.group(0)
        else:
            value = None
            
            
        message = {
            'valor': value,
            'match': match
        }
        return value
    
    def extrair_fatura(self) -> Optional[str]:
        padrao = r'No : (\w{2} \d+/\d+)'
        match = re.search(padrao, self.texto) 
        if match:
            value = match.group(1)
        else:
            value = None
            
            
        message = {
            'valor': value,
            'match': match
        }
        return value

    def extrair_valor(self) -> Optional[float]:
        padrao = r'Total (\d+,\d{2})'
        match = re.search(padrao, self.texto) 
        if match:
            value = match.group(1)
            value = value.replace('.', '').replace(',', '.')
            value = float(value)
        else:
            value = 0
            
            
        message = {
            'valor': value,
            'match': match
        }
        return value
    
    def extrair_desconto(self) -> Optional[float]:
        padrao = r'Total em descontos (\d+,\d{2})'
        match = re.search(padrao, self.texto) 
        if match:
            value = match.group(1)
            value = value.replace('.', '').replace(',', '.')
            value = float(value)
        else:
            value = 0
            
            
        message = {
            'valor': value,
            'match': match
        }
        return value
    
    def extrair_base_23(self) -> Optional[float]:
        padrao = r"([A])\s+(\d+%)+\s+(\d+\,\d+)+\s+(\d+\,\d+)+\s+(\d+\,\d+)"
        match = re.search(padrao, self.texto) 
        if match:
            value = match.group(3)
            value = value.replace('.', '').replace(',', '.')
            value = float(value)
        else:
            value = 0
            
            
        message = {
            'valor': value,
            'match': match
        }
        return value

    def extrair_iva_23(self) -> Optional[float]:
        padrao = r"([A])\s+(\d+%)+\s+(\d+\,\d+)+\s+(\d+\,\d+)+\s+(\d+\,\d+)"
        match = re.search(padrao, self.texto) 
        if match:
            value = match.group(5)
            value = value.replace('.', '').replace(',', '.')
            value = float(value)
        else:
            value = 0
            
            
        message = {
            'valor': value,
            'match': match
        }
        return value
    
    def extrair_base_06(self) -> Optional[float]:
        padrao = r"([B])\s+(\d+%)+\s+(\d+\,\d+)+\s+(\d+\,\d+)+\s+(\d+\,\d+)"
        match = re.search(padrao, self.texto) 
        if match:
            value = match.group(3)
            value = value.replace('.', '').replace(',', '.')
            value = float(value)
        else:
            value = 0
            
            
        message = {
            'valor': value,
            'match': match
        }
        return value
    
    def extrair_iva_06(self) -> Optional[float]:
        padrao = r"([B])\s+(\d+%)+\s+(\d+\,\d+)+\s+(\d+\,\d+)+\s+(\d+\,\d+)"
        match = re.search(padrao, self.texto) 
        if match:
            value = match.group(5)
            value = value.replace('.', '').replace(',', '.')
            value = float(value)
        else:
            value = 0
            
            
        message = {
            'valor': value,
            'match': match
        }
        return value
    
    def extrair_entidade(self) -> Optional[int]:
        value = 1
        message = {
            'valor': value,
            'match': 'sem padrao configurado'
        }        
        return value
    
    def extrair_linhas(self):
        linhas_texto = self.texto.split('\n')
        padrao_linha = r'^(.*?)\s+(\d+,\d{2})\s+([AB])$'
        padrao_linha_desconto = r'^(.*) -(\d+,\d{2})$'
        linhas_extraidas = []
        for linha in linhas_texto:
            match_linha = re.match(padrao_linha, linha)
            match_desconto = re.match(padrao_linha_desconto, linha)
            if match_linha:
                artigo = match_linha.group(1).strip()
                tx_iva = float(match_linha.group(3).replace('A', '0.23').replace('B', '0.06'))
                valor_bruto_civa = float(match_linha.group(2).replace(',', '.'))
                linhas_extraidas.append(FaturaLinhasCreate(artigo=artigo, tx_iva=tx_iva, valor=valor_bruto_civa))

            
            desconto_civa = 0.00
            if match_desconto and linhas_extraidas:
                desconto_civa = float(match_desconto.group(2).replace(',', '.'))
                linhas_extraidas[-1].valor = round(linhas_extraidas[-1].valor - desconto_civa, 2)
                linhas_extraidas[-1].desconto = desconto_civa
            
        if linhas_extraidas:
            for linha in linhas_extraidas:
                linha.iva = round(linha.valor * (linha.tx_iva / (1 + linha.tx_iva)), 2) 
                linha.base = round(linha.valor - linha.iva, 2)
                if linha.desconto:
                    linha.desconto = round(linha.desconto / (1 + linha.tx_iva), 2 )
                    linha.valor_bruto = round(linha.base + linha.desconto, 2)
                else:
                    linha.desconto = 0
                    linha.valor_bruto = round(linha.base, 2)
        return linhas_extraidas


    def run_extraction(self):
        try:
            self.cabec.entidade_id = 1
            self.cabec.data = self.extrair_data()
            self.cabec.nif = self.extrair_nif()
            self.cabec.nif_entidade = self.extrair_nif_entidade()
            self.cabec.fatura = self.extrair_fatura()
            self.cabec.valor = self.extrair_valor()
            self.cabec.desconto = self.extrair_desconto()
            base_23 = self.extrair_base_23()
            iva_23 = self.extrair_iva_23()
            base_06 = self.extrair_base_06()
            iva_06 = self.extrair_iva_06()
            self.cabec.ano = self.cabec.data.year
            self.cabec.mes = self.cabec.data.month
            self.cabec.base = base_23 + base_06 if base_23 and base_06 else None
            self.cabec.iva = round(iva_23 + iva_06, 2) if iva_23 and iva_06 else None 
            self.linhas = self.extrair_linhas()
            for linha in self.linhas:
                linha.data = self.cabec.data
                linha.fatura = self.cabec.fatura
                linha.base = round(linha.valor / (1 + linha.tx_iva), 2)
                linha.iva = linha.base * linha.tx_iva
                linha.ano = self.cabec.ano
                linha.mes = self.cabec.mes
            self.cabec.valor_bruto = sum([linha.valor_bruto for linha in self.linhas])
            logger.info(msg=f"FATURA EXTRACTION: {self.nome_ficheiro}", ThreadName="FaturaLidlExtractor", Application="pgsqlapi", _cabec=self.cabec.model_dump(mode="json", exclude_none=True), _linhas=[linha.model_dump(mode="json", exclude_none=True) for linha in self.linhas])
            return self.cabec, self.linhas
        except Exception as e:
            mensagem = str(e).strip().split("\n")[0]
            logger.error(msg=f"{type(e).__name__} - {mensagem}", ThreadName="FaturaLidlExtractor", Application="pgsqlapi", exc_info=e)
            raise ValueError(f"ERRO AO EXTRAIR FATURA: {mensagem}")




class FaturaMicrosoftExtractor:
    def __init__(self, texto, nome_ficheiro):
        self.texto = texto
        self.nome_ficheiro = nome_ficheiro
        self.cabec = FaturaCabecCreate()
        self.linhas = [FaturaLinhasCreate()]


    def extrair_data(self) -> datetime:
        funcao = get_funcao()
        padroes = [
            (r'\b\d{2}-\d{2}-\d{4}\b', '%d-%m-%Y'),
            (r'\b\d{2}/\d{2}/\d{4}\b', '%d/%m/%Y')
        ]
        match = None
        value = datetime(1900, 1, 1, 0, 0, 0)  # Valor padrão se nenhum padrão for encontrado
        
        for padrao, formato in padroes:
            match = re.search(padrao, self.texto)
            if match:
                date_str = match.group(0).replace('-', '/')
                value = datetime.strptime(date_str, formato.replace('-', '/'))
                break

        message = {
            'valor': value,
            'match': match
        }
        logger.log_info(message=message, tabela=tabela, funcao=funcao)
        return value
    
    def extrair_fatura(self) -> str | None:
        funcao = get_funcao()
        value = None
        match = None
        padrao1 = r'Invoice Number: (.*)'
        padrao2 = r'N.º de Fatura:\s*([\w\s/]+?)(?=\sData)' 
        padrao3 = r"(FT\s\w{2}\d{2}/\d{8})"
        match1 = re.search(padrao1, self.texto)
        match2 = re.search(padrao2, self.texto)
        match3 = re.search(padrao3, self.texto)
        if match1:
            value = match1.group(1)
            match = match1
        if match2:
            value = match2.group(1)
            match = match2
        if match3:
            value = match3.group(1)
            match = match3
        message = {
            'valor': value,
            'match': match
        }
        logger.log_info(message=message, tabela=tabela, funcao=funcao)
        return value

    def extrair_valor(self) -> float:
        funcao = get_funcao()
        match = None
        padrao1 = r'Total Amount: ([\d,.]+) EUR'
        padrao2 = r'Montante Total \(EUR\)\s*([\d,.]+)'
        match1 = re.search(padrao1, self.texto)
        match2 = re.search(padrao2, self.texto)
        if match1:
            match = match1
            value = match1.group(1)
            value = float(value.replace(' ', '').replace(',', '.'))
        if match2:
            match = match2
            value = match2.group(1)
            value = float(value.replace(' ', '').replace(',', '.'))
        if not match1 and not match2:
            match = 'padrao nao encontrado'
            value = 0.00
        message = {
            'valor': value,
            'match': match
        }
        logger.log_info(message=message, tabela=tabela, funcao=funcao)
        return value
    
    def extrair_iva_23(self) -> float:
        funcao = get_funcao()
        value = 0.00
        match = None
        padrao1 = r'Total VAT \(23,00%\):\s*([\d,.]+)'
        padrao2 = r'IVA \(\d+,\d+%\) (\d+,\d+)'
        match1 = re.search(padrao1, self.texto)
        match2 = re.search(padrao2, self.texto)
        if match1:
            match = match1
            value = match1.group(1)
            value = float(value.replace(' ', '').replace(',', '.'))
        if match2:
            match = match2
            value = match2.group(1)
            value = float(value.replace(' ', '').replace(',', '.'))


        message = {
            'valor': value,
            'match': match
        }
        logger.log_info(message=message, tabela=tabela, funcao=funcao)



        return value
    
    def extrair_iva_06(self) -> float:
        funcao = get_funcao()
        message = {
            'valor': 'sem padrao configurado', 
            'match': 'sem padrao configurado'
            }
        logger.log_info(message=message, tabela=tabela, funcao=funcao)
        return 0.00

    def extrair_linhas(self):
        value = None
        funcao = get_funcao()
        linhas_texto = self.texto.split('\n')
        linhas = []
        # padrao1 = r"([\w\s]+)\s+(\d+,\d+)\s+\d+,\d+\s+(\d+,\d+)\s+\(\d+,\d+%\)\s+(\d+,\d+)"
        # padrao2 = r"([^\d]+?)\s*([\+\-]?\d+)\s+(\d+)\s+(.+?)\s+(\d+,\d+)"
        # padrao3 = r"([\w\s]+)\s+(\d+,\d+)\s+(\d+,\d+)\s+(.+?)\s+(\d+,\d+)"
        padrao2020 = r"(?P<artigo>[\w\s\+\-]+) \d+ Total de Custos Antes do Imposto (?P<base>\d+,\d+)"
        padrao2021 = r"(?P<artigo>[\w\s]+) (?P<base>\d+,\d+)"
        padroes = [padrao2020, padrao2021]
        
        
        for linha in linhas_texto:
            for padrao in padroes:
                match = re.match(padrao, linha)
                if match:
                    artigo = match.group('artigo').strip()
                    base = float(match.group('base').replace(',', '.'))
                    desconto = 0.00
                    tx_iva = 0.23
                    iva = round(base * tx_iva, 2)
                    valor = base + iva
                    valor_bruto = base + desconto
                    linhas.append(Linhas(
                        artigo=artigo, 
                        base=base,
                        desconto=desconto,
                        valor_bruto=valor_bruto,
                        valor=valor,
                        iva=iva,
                        tx_iva=tx_iva,

                        ))
                    message = { 
                                'padrao': 'match2',
                                'match': match
                                }
                    logger.log_info(message=message, tabela=tabela, funcao=funcao) 
                    break               
            

        return linhas


    def run_extraction(self):
        try:
            self.cabec.entidade_id = 19 
            self.cabec.data = self.extrair_data()
            # self.cabec.nif = self.extrair_nif()
            # self.cabec.nif_entidade = self.extrair_nif_entidade()
            self.cabec.fatura = self.extrair_fatura()
            self.cabec.valor = self.extrair_valor()
            # self.cabec.desconto = self.extrair_desconto()
            # base_23 = self.extrair_base_23()
            iva_23 = self.extrair_iva_23()
            # base_06 = self.extrair_base_06()
            iva_06 = self.extrair_iva_06()
            self.cabec.iva = iva_23 + iva_06
            self.cabec.ano = self.cabec.data.year
            self.cabec.mes = self.cabec.data.month
            self.cabec.base = self.cabec.valor - self.cabec.iva


            self.linhas = self.extrair_linhas()
            for linha in self.linhas:
                linha.data = self.cabec.data
                linha.fatura = self.cabec.fatura
                linha.ano = self.cabec.ano
                linha.mes = self.cabec.mes



            self.cabec.valor_bruto = sum([linha.valor_bruto for linha in self.linhas])
            logger.info(msg=f"FATURA EXTRACTION: {self.nome_ficheiro}", ThreadName="FaturaMicrosoftExtractor", Application="pgsqlapi", _cabec=self.cabec.model_dump(mode="json", exclude_none=True), _linhas=[linha.model_dump(mode="json", exclude_none=True) for linha in self.linhas])
            return self.cabec, self.linhas
        except Exception as e:
            mensagem = str(e).strip().split("\n")[0]
            logger.error(msg=f"{type(e).__name__} - {mensagem}", ThreadName="FaturaMicrosoftExtractor", Application="pgsqlapi", exc_info=e)
            raise ValueError(f"ERRO AO EXTRAIR FATURA: {mensagem}")



class FaturaBox111Extractor:
    def __init__(self, texto, nome_ficheiro):
        self.texto = texto
        self.nome_ficheiro = nome_ficheiro
        self.cabec = FaturaCabecCreate()
        self.linhas = [FaturaLinhasCreate()]



    def extrair_fatura(self) -> str:
        padrao = r'F\s*a\s*c\s*t\s*u\s*r\s*a\s*\s*-\s*R\s*e\s*c\s*i\s*b\s*o\s*([\w\d]+)\s*/\s*(\d{3,6})'
        match = re.search(padrao, self.texto, re.IGNORECASE)
        if match:
            serie = match.group(1)
            numero = match.group(2)
            return f'FAT:{serie}/{numero}'
        return None

    def extrair_nif(self) -> str:
        padrao = r'N\s*I\s*F\s*:\s*(\d{9})'
        match = re.search(padrao, self.texto, re.IGNORECASE)
        if match:
            return match.group(1)

        return None
    def extrair_nif_entidade(self) -> str:
        padrao = r'N\s*º\s*C\s*o\s*n\s*t\s*r\s*i\s*b\s*u\s*i\s*n\s*t\s*e\s*:\s*(\d{9})'
        match = re.search(padrao, self.texto, re.IGNORECASE)
        if match:
            return match.group(1)
        return None

    def extrair_base(self) -> float:
        padrao = r'T otal ilíquido:[:\s]*([\d,.]+)€'
        match = re.search(padrao, self.texto)
        return float(match.group(1).replace(',', '.')) if match else 0.0

    def extrair_desconto(self) -> float:
        padrao = r'T otal descontos:[:\s]*([\d,.]+)€'
        match = re.search(padrao, self.texto)
        return float(match.group(1).replace(',', '.')) if match else 0.0


    def extrair_iva(self) -> float:
        padrao = r'T otal IV A :[:\s]*([\d,.]+)€'
        match = re.search(padrao, self.texto)
        return float(match.group(1).replace(',', '.')) if match else 0.0

    def extrair_valor(self) -> float:
        padrao = r'Totaldo D ocum ento:[:\s]*([\d,.]+)€'
        match = re.search(padrao, self.texto)
        return float(match.group(1).replace(',', '.')) if match else 0.0
    
    def extrair_data(self) -> datetime:
        padrao_data = r'D\s*a\s*t\s*a\s*:\s*(\d{4}-\d{2}-\d{2})'
        padrao_hora = r'H\s*o\s*r\s*a\s*:\s*(\d{2}:\d{2}:\d{2})'
        
        match_data = re.search(padrao_data, self.texto, re.IGNORECASE)
        match_hora = re.search(padrao_hora, self.texto, re.IGNORECASE)

        if match_data and match_hora:
            data_str = match_data.group(1)
            hora_str = match_hora.group(1)
            data = datetime.strptime(data_str, '%Y-%m-%d').date()
            hora = datetime.strptime(hora_str, '%H:%M:%S').time()
            return datetime.combine(data, hora)

        return datetime(1900, 1, 1, 0, 0, 0)

    def extrair_linhas(self):
        padrao = re.compile(
                                r"""
                                (?P<artigo>.+?)         # artigo (tudo até antes dos números)
                                \s+(?P<quantidade>\d+),            # quantidade (ex: 1)
                                \s+(?P<desconto>\d+,\d+)%          # desconto (ex: 0,00%)
                                \s+(?P<tx_iva>\d+)%                # taxa de IVA (ex: 23%)
                                \s+(?P<valor_unitario>\d+,\d+)€    # valor unitário (ex: 22,68€)
                                \s+(?P<valor>\d+,\d+)€             # total (ex: 27,90€)
                                """,
                                re.VERBOSE
                            )
        linhas = []
        matches = padrao.finditer(self.texto)
        
        for match in matches:
            linha = FaturaLinhasCreate()
            linha.artigo = match.group('artigo').strip()
            linha.artigo = re.sub(r'(?<=\w)\s(?=\w)', '', linha.artigo)
            linha.artigo = re.sub(r'\s{2,}', ' ', linha.artigo)
            linha.desconto = float(match.group('desconto').replace(',', '.'))
            linha.tx_iva = float(match.group('tx_iva')) / 100
            linha.base = float(match.group('valor_unitario').replace(',', '.'))
            linha.valor = float(match.group('valor').replace(',', '.'))
            linha.valor_bruto = round(linha.base + linha.desconto, 2)
            linha.iva = round(linha.valor - linha.base, 2)
            linhas.append(linha)
        
        return linhas if linhas else [FaturaLinhasCreate()]

        


    def run_extraction(self):
        self.cabec.fatura = self.extrair_fatura()
        self.cabec.nif = self.extrair_nif()
        self.cabec.nif_entidade = self.extrair_nif_entidade()
        self.cabec.base = self.extrair_base()
        self.cabec.desconto = self.extrair_desconto()
        self.cabec.iva = self.extrair_iva()
        self.cabec.valor = self.extrair_valor()
        self.cabec.valor_bruto = round(self.cabec.base + self.cabec.desconto, 2)
        self.cabec.data = self.extrair_data()
        self.cabec.ano = self.cabec.data.year
        self.cabec.mes = self.cabec.data.month
        self.cabec.entidade_id = 34


        self.linhas = self.extrair_linhas()
        for linha in self.linhas:
            linha.data = self.cabec.data
            linha.ano = self.cabec.ano
            linha.mes = self.cabec.mes
            linha.fatura = self.cabec.fatura
            

        return self.cabec, self.linhas
        