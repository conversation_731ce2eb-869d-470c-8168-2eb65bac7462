import streamlit as st
from src.services.api import api_get, api_update
from src.comprovativos.classComprovativo import Comprovativo
from src.segmentos.classSegmento import Segmento
from src.contas.classConta import Conta
import time


def get_comprovativos(selected_user):
    response = api_get(tabela='contas')
    contas = [Conta(**conta) for conta in response.json()]
    contas_user = [conta for conta in contas if conta.utilizador == selected_user]
    ids_contas = [conta.id for conta in contas_user]
    response = api_get(tabela='comprovativos', params={'conta_id': ids_contas})
    return [Comprovativo(**comprovativo) for comprovativo in response.json()]
    


def get_segmentos():
    response = api_get(tabela='segmentos')
    return [Segmento(**segmento) for segmento in response.json()]


def render_comprovativo(comprovativo, segmentos):
    with st.expander(f"{comprovativo.movimento}"):
        col1, col2 = st.columns([3, 2])

        with col1:
            st.write(f"**Data:** {comprovativo.data_valor}")
            st.write(f"**Movimento:** {comprovativo.movimento}")
            st.write(f"**Valor:** {comprovativo.valor} €")
            st.write(f"**Entidade:** {comprovativo.entidade}")

        with col2:
            segmento_selecionado = st.selectbox(
                                                        "Segmento",
                                                        options=segmentos,
                                                        format_func=lambda seg: seg.segmento,
                                                        index=0,
                                                        key=f"segmento_{comprovativo.id}"
                                                    )

            is_valid = segmento_selecionado != None
            btn_save = st.button("save", key=f"save_{comprovativo.id}", disabled=not is_valid)
            if btn_save and is_valid:
                response = api_update(tabela='comprovativos', data={'segmento_id': segmento_selecionado.id, 'id': comprovativo.id})
                if response.status_code == 200:
                    st.success("atualizado.")
                    time.sleep(1)
                    st.rerun()
                else:
                    st.error("erro ao atualizar.")
    



def main():
    if 'segmentos' not in st.session_state:
        st.session_state['segmentos'] = get_segmentos()
    segmentos = st.session_state['segmentos']

    if 'selected_user' in st.session_state:
        selected_user = st.session_state['selected_user']
        comprovativos = get_comprovativos(selected_user)

        st.subheader(f"Comprovativos Não Classificados ({len(comprovativos)})")
        for comprovativo in comprovativos:
            render_comprovativo(comprovativo, segmentos)

if __name__ == "__main__":
    main()