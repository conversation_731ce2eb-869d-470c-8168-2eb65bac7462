/pgsql/
  ├── comum/
  │    ├── .env
  │    ├── __init__.py
  │    ├── models/
  │    │    ├── __init__.py
  │    │    ├── comprova_padroes.py
  │    │    ├── comprovativos.py
  │    │    ├── contactos.py
  │    │    ├── contactos_telefones.py
  │    │    ├── contas.py
  │    │    ├── entidades.py
  │    │    ├── estados.py
  │    │    ├── eventos.py
  │    │    ├── faturas_cabec.py
  │    │    ├── faturas_linhas.py
  │    │    ├── ficheiros.py
  │    │    ├── recibos.py
  │    │    ├── recibos_codigos.py
  │    │    └── segmentos.py
  │    └── services/
  │         ├── __init__.py
  │         └── logger.py
  ├── pgsql-api/
  │    ├── .env
  │    ├── Dockerfile
  │    ├── requirements.txt
  │    └── app/
  │         ├── __init__.py
  │         ├── main.py
  │         ├── _queries/
  │         ├── database/
  │         ├── routes/
  │         └── workers/
  ├── pgsql-ops/
  │    ├── Dockerfile
  │    ├── requirements.txt
  │    └── app/
  │         ├── main.py
  │         ├── extractors/
  │         └── services/
  ├── pgsql-web/
  │    ├── Dockerfile
  │    ├── requirements.txt
  │    └── app/
  │         ├── .env
  │         ├── main.py
  │         ├── pages/
  │         └── src/
  ├── .gitignore
  ├── docker-compose.yml
  ├── read.md
  ├── run_api.py



python3 -m venv venv
source venv/bin/activate
pip freeze > requirements.txt
pip install -r requirements.txt


$env:PYTHONPATH="C:\Python\pgsql"

export PYTHONPATH="/Users/<USER>/Python/pgsql"

pip install pip-review

pip-review --auto

# pgsqlapi

cd pgsql-api
python3 -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt
PYTHONPATH=.. uvicorn app.main:app
PYTHONPATH=.. fastapi run app/main.py --reload
uvicorn pgsqlapi.app.main:app --host 127.0.0.1 --port 5002 --reload

# pgsqlops

cd pgsqlops
python3 -m venv venv
source .venv/bin/activate
pip install -r requirements.txt
python watcher/watcher.py

$env:PYTHONPATH="C:\Python\pgsql"

# pgsqlweb

cd pgsql-web
python3 -m venv venv
source .venv/bin/activate
pip install -r requirements.txt
streamlit run app/main.py
