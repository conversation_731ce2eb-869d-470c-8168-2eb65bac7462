SET timezone = 'Europe/Lisbon';

INSERT INTO public.entidades(id, entidade) 
  VALUES 
        (0, 'null'),
        (1, 'lidl'), 
        (2, 'continente'), 
        (3, 'pingo_doce'), 
        (4, 'pao_de_acucar'), 
        (5, 'auchan'), 
        (6, 'mercadona'), 
        (7, 'mcdonald'), 
        (8, 'empifarma'), 
        (9, 'meo'), 
        (10, 'mbway'), 
        (11, 'estado'), 
        (12, 'uber'), 
        (13, 'zara'), 
        (14, 'via_verde'),
        (15, 'burger_king'),
        (16, 'airbnb'), 
        (17, 'woo'), 
        (18, 'cgd'), 
        (19, 'microsoft'),
        (20, 'uber_eats'),
        (21, 'amazon'), 
        (22, 'medusa'), 
        (23, 'insurama'), 
        (24, 'nos'), 
        (25, 'spliiit'), 
        (26, 'riot'), 
        (27, 'worten'), 
        (28, 'satander'), 
        (29, 'cepsa'), 
        (30, 'pro4matic'), 
        (31, 'galp'), 
        (32, 'repsol'), 
        (33, 'friends');





INSERT INTO public.contas(id,  conta, utilizador, entidade_id) 
  VALUES 
  (0, 'null', 'null', 0),
  (1, '0507021920600', 'ricardo', 18),
  (2, '0507021920261', 'ricardo', 18),
  (3, '53710935020', 'neide', 28), 
  (4, '55082564020', 'neide', 28);




INSERT INTO public.segmentos(id, segmento, categoria, icon) 
  VALUES 
  (0, 'null', 'null', NULL),
  (1, 'deposito', 'banco', NULL),
  (2, 'levantamento', 'banco', NULL),
  (3, 'manutencao_conta', 'banco', NULL),
  (4, 'transferencia', 'banco', NULL),
  (5, 'combustivel', 'habitacao', NULL),
  (6, 'oficina', 'habitacao', NULL),
  (7, 'via_verde', 'estado', NULL),
  (8, 'antivirus', 'null', NULL),
  (9, 'vpn', 'digital', NULL),
  (10, 'cloud', 'digital', NULL),
  (11, 'gaming', 'digital', NULL),
  (12, 'internet', 'habitacao', NULL),
  (13, 'telemovel', 'digital', NULL),
  (14, 'poupanca', 'economia', NULL),
  (15, 'prestacao', 'economia', NULL),
  (16, 'investimento', 'economia', NULL),
  (17, 'curso', 'educacao', NULL),
  (18, 'livro', 'educacao', NULL),
  (19, 'agua', 'habitacao', NULL),
  (20, 'bricolage', 'habitacao', NULL),
  (21, 'gas', 'habitacao', NULL),
  (22, 'luz', 'habitacao', NULL),
  (23, 'mercearia', 'habitacao', NULL),
  (24, 'mobilia', 'habitacao', NULL),
  (25, 'comissao', 'banco', NULL),
  (26, 'imposto_selo', 'estado', NULL),
  (27, 'irs', 'estado', NULL),
  (28, 'assinatura_digital', 'digital', NULL),
  (29, 'actividade_extra', 'lazer', NULL),
  (30, 'cafe_bar', 'lazer', NULL),
  (31, 'cinema', 'lazer', NULL),
  (32, 'hotelaria', 'lazer', NULL),
  (33, 'museu', 'lazer', NULL),
  (34, 'restauracao', 'lazer', NULL),
  (35, 'roupa', 'lazer', NULL),
  (36, 'tabaco', 'lazer', NULL),
  (37, 'tecnologia', 'lazer', NULL),
  (38, 'viagem', 'lazer', NULL),
  (39, 'premio', 'rendimento', NULL),
  (40, 'salario', 'rendimento', NULL),
  (41, 'cabeleireiro', 'saude', NULL),
  (42, 'consulta_medica', 'saude', NULL),
  (43, 'farmacia', 'saude', NULL),
  (44, 'oftalmologista', 'saude', NULL),
  (45, 'dentista', 'saude', NULL),
  (46, 'seguro_carro', 'seguro', NULL),
  (47, 'seguro_casa', 'seguro', NULL),
  (48, 'seguro_saude', 'seguro', NULL),
  (49, 'seguro_telemovel', 'seguro', NULL),
  (50, 'crypto', 'digital', NULL),
  (51, 'rent_a_car', 'lazer', NULL),
  (52, 'iuc', 'estado', NULL),
  (53, 'seg_social', 'estado', NULL),
  (64, 'ginasio', 'saude', NULL),
  (65, 'seguro_pc', 'seguro', NULL);

INSERT INTO public.estados(estado, detalhe, cor) 
VALUES 
(0, 'PENDENTE', 'yellow'),
(99, 'ERRO', 'red'),
(100, 'OK', 'green'),
(200, 'PROCESSADO', 'green');


INSERT INTO public.comprova_padroes("id_chave", "padrao", "segmento_id", "entidade_id") 
  VALUES 
  (1, 'LEVANTAMENTO', 2, 18), 
  (2, 'MANUTENCAO CONTA', 3, 18), 
  (3, '%MANUT.CONTA%', 3, 28), 
  (4, 'TRF P2P 9', 4, 10), 
  (5, 'TRF MBWAY', 4, 10), 
  (6, 'BX VALOR 03-TRANSACCO', 7, 14), 
  (7, 'MEO TMN', 13, 9), 
  (8, 'TRF CXDAPP', 14, 18), 
  (9, 'CONTINENTE', 23, 2), 
  (10, 'LIDL', 23, 1), 
  (11, 'PINGO DOCE', 23, 3), 
  (12, 'PAO DE ACUCAR', 23, 4), 
  (13, 'AUCHAN ', 23, 5), 
  (14, 'COMPRA MERCADONA', 23, 6), 
  (15, '%COMISSAO %', 25, 18), 
  (16, 'IMPOSTO SELO', 26, 11), 
  (17, 'IMPOSTO DO SELO', 26, 11), 
  (19, 'MCDONALD', 34, 7), 
  (20, 'UBER EATS', 34, 12), 
  (21, 'ZARA', 35, 13), 
  (22, 'EMPIFARMA', 40, 8), 
  (23, 'CINEMA', 31, 24), 
  (35, 'AIRBNB', 32, 16), 
  (36, 'COMPRA MBWAY WOO', 13, 17), 
  (37, 'MICROSOFT', 10, 19), 
  (38, 'COMPRAS C.DEB NAVEGAD', 34, 22), 
  (39, 'IUC', 52, 11), 
  (41, 'COMPRA INSURAMA', 65, 23), 
  (42, 'COMPRA SPLIIIT', 28, 25), 
  (43, 'COMPRAS C.DEB PRO4MAT', 6, 30);

