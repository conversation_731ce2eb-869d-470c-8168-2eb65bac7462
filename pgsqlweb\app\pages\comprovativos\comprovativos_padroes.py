import streamlit as st
from src.services.supabase_client import connect
from src.comprovativos.classComprovativo import ComprovativoPadrao
from src.entidades.classEntidade import Entidade
from src.segmentos.classSegmento import Segmento

supabase = connect()

def get_entidades() -> list[Entidade]:
    entidades = supabase.table('entidades').select('*').order('id_chave', desc=False).execute().data 
    entidades = [Entidade(**entidade) for entidade in entidades]
    return entidades


def get_segmentos() -> list[Segmento]:
    segmentos = supabase.table('segmentos').select('*').order('segmento', desc=False).execute().data 
    segmentos = [Segmento(**segmento) for segmento in segmentos]
    return segmentos


segmentos = get_segmentos()
entidades = get_entidades()





def main():
    st.subheader("PADRÕES DE COMPROVATIVOS")

    
    response = supabase.table("comprova_padroes").select("*").order("id_chave", desc=False).execute()
    if response.data:
        num_columns = 3
        grouped_items = [response.data[i::num_columns] for i in range(num_columns)]
        cols = st.columns(num_columns)
        for col, items in zip(cols, grouped_items):
            for item in items:
                with col.expander(f"movimento ILIKE '{item['padrao']}'"):
                    item = ComprovativoPadrao(**item)
                    is_saved = 0
                    col1, col2, col3 = st.columns([3,1,1])
                    with col1:
                        segmento_selecionado = st.selectbox(
                                                            "segmento",
                                                            options=segmentos,
                                                            format_func=lambda segmento: segmento.segmento,
                                                            key=f"segmento_{item.id_chave}",
                                                            index=[s.id_chave for s in segmentos].index(item.segmento_id) 
                                                        )
                    with col2:
                        st.write("")
                        if st.button("save", key=f"saveSegmento_{item.id_chave}"):
                                response = supabase.table("comprova_padroes").select("*").eq("id_chave", item.id_chave).execute()
                                padrao_old = ComprovativoPadrao(**response.data[0])
                                segmento_old = padrao_old.segmento_id
                                if segmento_old != segmento_selecionado.id_chave:
                                    response_insert = supabase.table("comprova_padroes").update({"segmento_id": segmento_selecionado.id_chave}).eq("id_chave", item.id_chave).execute()
                                    if response_insert.data:
                                        is_saved = 2
                                else:
                                    is_saved = 1
                    with col3:
                        if is_saved == 2:
                            st.write("")
                            st.success("saved")
                        elif is_saved == 1:
                            st.write("")
                            st.warning("not saved")


                    col1, col2, col3 = st.columns([3,1,1])
                    is_saved = False
                    with col1:
                        entidade_selecionado = st.selectbox(
                                                            "entidade",
                                                            options=entidades,
                                                            format_func=lambda entidade: entidade.entidade,
                                                            key=f"entidade_{item.id_chave}",
                                                            index=[s.id_chave for s in entidades].index(item.entidade_id) 
                                                        )
                    with col2:
                        st.write("")
                        if st.button("save", key=f"saveEntidade_{item.id_chave}"):
                                response = supabase.table("comprova_padroes").select("*").eq("id_chave", item.id_chave).execute()
                                padrao_old = ComprovativoPadrao(**response.data[0])
                                entidade_old = padrao_old.entidade_id
                                if entidade_old != entidade_selecionado.id_chave:
                                    response_insert = supabase.table("comprova_padroes").update({"entidade_id": entidade_selecionado.id_chave}).eq("id_chave", item.id_chave).execute()
                                    if response_insert.data:
                                        is_saved = 2
                                else:
                                    is_saved = 1
                    with col3:
                        if is_saved == 2:
                            st.write("")
                            st.success("saved")
                        elif is_saved == 1:
                            st.write("")
                            st.warning("not saved")
  
    else:               

        st.write("Nenhum dado encontrado.")


if __name__ == "__main__":
    main()