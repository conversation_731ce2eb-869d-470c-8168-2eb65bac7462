from datetime import datetime
from typing import Optional
from uuid import UUID
from pydantic import BaseModel, Field


class Evento(BaseModel):
    id: Optional[int] = Field(default=None)
    id_chave: Optional[UUID] = Field(default=None)
    tipo: Optional[str] = Field(default=None)
    tabela: Optional[str] = Field(default=None)
    fkid: Optional[int] = Field(default=None)
    fkid_chave: Optional[UUID] = Field(default=None)
    estado: Optional[int] = Field(default=0)
    obs: Optional[str] = Field(default=None)
    data_criacao: Optional[datetime] = Field(default=None)
    data_alteracao: Optional[datetime] = Field(default=None)


    def dict(self, *args, **kwargs):
        original_dict = super().model_dump(*args, **kwargs)
        for key, value in original_dict.items():
            if isinstance(value, datetime):
                original_dict[key] = value.isoformat()
        return original_dict
    


class Estado(BaseModel):
    id_chave: Optional[UUID] = Field(default=None)
    estado: Optional[int] = Field(default=0)
    detalhe: Optional[str] = Field(default=None)
    cor: Optional[str] = Field(default=None)
    data_criacao: Optional[datetime] = Field(default=None)
    data_alteracao: Optional[datetime] = Field(default=None)