FROM python:3.13-slim

WORKDIR /app

# Copia apenas o requirements.txt primeiro para aproveitar o cache do Docker
COPY requirements_api.txt .

# Instala as dependências do sistema necessárias para o psycopg2
# RUN apt-get update && \
#     apt-get install -y --no-install-recommends \
#     gcc \
#     python3-dev \
#     libpq-dev && \
#     rm -rf /var/lib/apt/lists/*



RUN apt-get update && apt-get install -y \
    gcc \
    libpq-dev \
    build-essential \
    && rm -rf /var/lib/apt/lists/*






# Instala as dependências Python
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements_api.txt

# Copia o resto dos arquivos
COPY . .

# Expõe a porta da API
EXPOSE 5000

# Comando para correr a API com uvicorn
# CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "5000", "--reload"]