from src.contactos.classContacto import ContactoClean, ContactoTelefone
from src.services.api import api_get, api_create, api_update
from src.eventos.classEvento import Evento

class WorkerContacto:
    def update_evento(self, evento_id, estado, obs=""):
        api_update(tabela="eventos", data={"estado": estado, "obs": obs, "id": evento_id})


    def get_contacto(self, id: int):
        response = api_get(tabela=f'contactos/{id}')
        return ContactoClean(**response.json()) if response.status_code == 200 else None

    def processar_evento(self, evento: Evento):
        try:
            if evento.tipo == 'INSERT':
                contacto = self.get_contacto(evento.fkid)
                if contacto.origem == '<EMAIL>':
                    for telefone in contacto.telefones:
                        last9 = telefone.field[-9:]
                        cont_trf = last9[:3] + last9[-3:]
                        contactotelefone = ContactoTelefone(
                                                                contacto_id=contacto.id_chave,
                                                                telefone=telefone.field,
                                                                tipo=telefone.label,
                                                                cont_trf=cont_trf
                                                            )
                        api_create(tabela='contactos_telefones', dados=contactotelefone.model_dump(exclude_unset=True, exclude_none=True))
                    self.update_evento(evento_id=evento.id, estado=200, obs=None)


                elif contacto.origem == '<EMAIL>':
                    for telefone in contacto.telefones:
                        cont_trf = telefone.field[-4:]
                        contactotelefone = ContactoTelefone(
                                                                contacto_id=contacto.id_chave,
                                                                telefone=telefone.field,
                                                                tipo=telefone.label,
                                                                cont_trf=cont_trf
                                                            )
                        api_create(tabela='contactos_telefones', dados=contactotelefone.model_dump(exclude_unset=True, exclude_none=True))
                    self.update_evento(evento_id=evento.id, estado=200, obs=None)

                    
                else:
                    self.update_evento(evento_id=evento.id, estado=100, obs="origem não configurada")

            
        except Exception as e:
            self.update_evento(evento_id=evento.id, estado=99, obs='worker_erro: '+ str(e))









