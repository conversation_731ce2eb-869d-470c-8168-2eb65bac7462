import os
import httpx
from dotenv import load_dotenv
from src.services.logger import setup_logger
logger = setup_logger()

load_dotenv()

API_URL = os.getenv("API_URL")  


def api_get(tabela: str, params: dict = None):
    try:
        url = f"{API_URL}/{tabela}"
        response = httpx.get(url, params=params)
        response.raise_for_status()
        json_data = response.json()
        
        if isinstance(json_data, list):
            logger.info(
                        msg="",
                        Table=tabela.lower(),
                        ThreadName="api_get",
                        RequestPath=url,
                        RequestMethod="GET",
                        StatusCode=response.status_code,
                        Application="pgsql-st",
                        SourceContext="api_get",
                        Result=json_data
                    )
        elif isinstance(json_data, dict):
            logger.info(
                        msg="",
                        Table=tabela.lower(),
                        ThreadName="api_get",
                        RequestPath=url,
                        RequestMethod="GET",
                        StatusCode=response.status_code,
                        Application="pgsql-st",
                        SourceContext="api_get",
                        **json_data
                    ) 
        
        return response
    except Exception as e:
        logger.error(f"Erro ao comunicar com API. URL: {url.replace('%', '%%')}", exc_info=e, ThreadName="api_get")
        return None


def api_create(tabela: str, data: dict):
    try:
        url = f"{API_URL}/{tabela}"
        response = httpx.post(url, json=data)
        response.raise_for_status()
        logger.warning(msg="", Table=tabela.lower(), ThreadName="api_create", RequestPath=url, RequestMethod="POST", StatusCode=response.status_code, Application="pgsql-st", SourceContext="api_create", **response.json())
        return response
    except Exception as e:
        logger.error(f"Erro ao comunicar com API. URL: {url.replace('%', '%%')}", exc_info=e, ThreadName="api_create")
        return None


def api_update(tabela: str, data: dict):
    try:
        url = f"{API_URL}/{tabela}/{data['id']}"
        response = httpx.patch(url, json=data)
        response.raise_for_status()
        logger.warning(msg="", Table=tabela.lower(), ThreadName="api_update", RequestPath=url, RequestMethod="PATCH", StatusCode=response.status_code, Application="pgsql-st", SourceContext="api_update", data=data)
        return response
    except Exception as e:
        logger.error(f"Erro ao comunicar com API. URL: {url.replace('%', '%%')}", exc_info=e, ThreadName="api_update")
        return None


def api_delete(tabela: str, data: dict):
    try:
        url = f"{API_URL}/{tabela}/{data['id']}"
        response = httpx.delete(url)
        response.raise_for_status()
        logger.info(msg="", Table=tabela.lower(), ThreadName="api_delete", RequestPath=url, RequestMethod="DELETE", StatusCode=response.status_code, Application="pgsql-st", SourceContext="api_delete", result=response.json())
        return response
    except Exception as e:
        logger.error(f"Erro ao comunicar com API. URL: {url.replace('%', '%%')}", exc_info=e, ThreadName="api_delete")
        return None

def api_eventos_pendentes():
    try:
        url = f"{API_URL}/eventos/pendentes"
        response = httpx.get(url, timeout=30.0) 
        if response.status_code == 200:
            return response
        else:
            logger.warning(f"Resposta não esperada da API. Status: {response.status_code}, URL: {url.replace('%', '%%')}", ThreadName="api_eventos_pendentes")
            return None
    except Exception as e:
        logger.error(f"Erro ao comunicar com API. URL: {url.replace('%', '%%')}", exc_info=e, ThreadName="api_eventos_pendentes")
        return None