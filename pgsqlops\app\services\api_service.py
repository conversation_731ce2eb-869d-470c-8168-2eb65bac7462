from typing import Optional, Dict, Any, List
from httpx import Client
from comum.services.config import PAPERLESS_API_URL, PAPERLESS_API_KEY, PGSQLAPI_URL
from comum.models.ficheiros import FicheiroCreate
from comum.services.logger import setup_logger
from comum.models.comprovativos import ComprovativoCreate, ComprovativoBase, ComprovativoUpdate
from comum.models.comprova_padroes import ComprovaPadraoBase, ComprovaPadraoCreate, ComprovaPadraoUpdate
from comum.models.recibos import ReciboCreate, ReciboBase
from comum.models.paperless import PaperlessModel, PaperlessDocument, PaperlessTagModel
from comum.models.eventos import EventoBase, EventoUpdate
from comum.models.contas import ContaBase, ContaCreate, ContaUpdate
from comum.models.ficheiros import FicheiroBase, FicheiroCreate, FicheiroUpdate
from comum.models.faturas_cabec import FaturaCabecBase, FaturaCabecCreate, FaturaCabecUpdate
from comum.models.faturas_linhas import FaturaLinhasBase, FaturaLinhasCreate, FaturaLinhasUpdate

logger = setup_logger()

class ApiService:
    def __init__(self):
        self.paperless_url = PAPERLESS_API_URL
        self.paperless_key = PAPERLESS_API_KEY
        self.pgsql_url = PGSQLAPI_URL

##############################################################################################                ###########################################################################
##############################################################################################                ###########################################################################
##############################################################################################    PAPERLESS   ###########################################################################
##############################################################################################                ###########################################################################
##############################################################################################                ###########################################################################

    def get_unprocessed_documents(self) -> Optional[PaperlessModel]:
        try:
            with Client() as client:
                url = f"{self.paperless_url}documents/"
                headers = {'Authorization': f'Token {self.paperless_key}'}
                params = {"custom_field_query": '["processado", "exact", false]'}
                response = client.get(url, headers=headers, params=params, )
                response.raise_for_status()
                nao_processados = PaperlessModel(**response.json())
                return nao_processados
                
        except Exception as e:
            logger.critical(msg=f"Erro na recolha de documentos não processados: {type(e).__name__} - {str(e)}", ThreadName="OpsWorker", Application="pgsqlops", exc_info=True) # type: ignore
            return None



    def update_document(self, documento_id: int, payload: Dict[str, Any]) -> Optional[PaperlessDocument]:
        try:
            with Client() as client:
                url = f"{self.paperless_url}documents/{documento_id}/"
                headers = {'Authorization': f'Token {self.paperless_key}'}
                response = client.patch(url, headers=headers, json=payload)
                response.raise_for_status()
                if response.status_code == 200:
                    logger.warning(msg=f"DOCUMENTO NGX ATUALIZADO: {documento_id}", ThreadName="ApiService", Application="pgsqlops", dados=payload)
                    doc_atualizado = PaperlessDocument(**response.json())
                    return doc_atualizado
                return None
        except Exception as e:
            logger.critical(msg=f"Erro ao atualizar documento {documento_id}: {type(e).__name__} - {str(e)}",ThreadName="ApiService",Application="pgsqlops",exc_info=True) # type: ignore
            return None
        

    def get_tag_by_name(self, tag_name: str) -> Optional[PaperlessTagModel]:
        try:
            with Client() as client:
                url = f"{self.paperless_url}tags/?name__iexact={tag_name}"
                headers = {'Authorization': f'Token {self.paperless_key}'}
                response = client.get(url, headers=headers)
                response.raise_for_status()
                if response.status_code == 200:
                    resposta = PaperlessTagModel(**response.json())
                    return resposta
                        
                return None
        except Exception as e:
            logger.error(msg=f"Erro ao obter ID da tag {tag_name}: {type(e).__name__} - {str(e)}",ThreadName="ApiService",Application="pgsqlops", exc_info=True) # type: ignore


##########################################################################################################################################################################################
##############################################################################################                ###########################################################################
############################################################################################## PGSQL DATABASE ###########################################################################
##############################################################################################                ###########################################################################
##########################################################################################################################################################################################

################################################################################### COMPROVATIVOS ########################################################################################

    def create_comprovativo(self, comprovativo: ComprovativoCreate) -> ComprovativoBase:
        try:
            with Client() as client:
                url = f"{self.pgsql_url}/comprovativos"
                response = client.post(url, content=comprovativo.model_dump_json(exclude_none=True), headers={"Content-Type": "application/json"})
                response.raise_for_status()
                
                if response.status_code != 200:
                    logger.error(msg=f"Erro ao criar comprovativo: {response.status_code} - {response.text}",ThreadName="ApiService",Application="pgsqlops", exc_info=True) # type: ignore
                    raise Exception(f"Erro ao criar comprovativo: {response.status_code} - {response.text}")
                return ComprovativoBase(**response.json()) if response.status_code == 200 else response.json()
                
        except Exception as e:
            logger.error(msg=f"Erro ao criar comprovativo: {str(e)}",ThreadName="ApiService",Application="pgsqlops", exc_info=True) # type: ignore

    def update_comprovativo(self, comprovativo_id:int, comprovativo: ComprovativoUpdate) -> ComprovativoBase:
        try:
            with Client() as client:
                url = f"{self.pgsql_url}/comprovativos/{comprovativo_id}"
                response = client.patch(url, content=comprovativo.model_dump_json(exclude_none=True), headers={"Content-Type": "application/json"})
                response.raise_for_status()
                
                if response.status_code != 200:
                    logger.error(msg=f"Erro ao atualizar comprovativo: {response.status_code} - {response.text}",ThreadName="ApiService",Application="pgsqlops", exc_info=True) # type: ignore
                    raise Exception(f"Erro ao atualizar comprovativo: {response.status_code} - {response.text}")
                return ComprovativoBase(**response.json()) if response.status_code == 200 else response.json()
                
        except Exception as e:
            logger.error(msg=f"Erro ao atualizar comprovativo: {str(e)}",ThreadName="ApiService",Application="pgsqlops", exc_info=True) # type: ignore


    def procura_comprovativo(self, comprovativo_id: int) -> ComprovativoBase:
        try:
            with Client() as client:
                url = f"{self.pgsql_url}/comprovativos/{comprovativo_id}"
                response = client.get(url)
                response.raise_for_status()
                return ComprovativoBase(**response.json())
        except Exception as e:
            logger.error(msg=f"Erro ao procurar comprovativo: {str(e)}",ThreadName="ApiService",Application="pgsqlops", exc_info=True) # type: ignore

################################################################################### RECIBOS ########################################################################################

    def create_recibo(self, recibo: ReciboCreate) -> ReciboBase:
        try:
            with Client() as client:
                url = f"{self.pgsql_url}/recibos"
                response = client.post(url, content=recibo.model_dump_json(exclude_none=True), headers={"Content-Type": "application/json"})
                response.raise_for_status()
                if response.status_code != 200:
                    logger.error(msg=f"Erro ao criar recibo: {response.status_code} - {response.text}",ThreadName="ApiService",Application="pgsqlops", exc_info=True) # type: ignore
                return ReciboBase(**response.json()) if response.status_code == 200 else response.json()
                
        except Exception as e:
            logger.error(msg=f"Erro ao criar recibo: {str(e)}",ThreadName="ApiService",Application="pgsqlops", exc_info=True) # type: ignore

################################################################################### EVENTOS ########################################################################################
    def get_eventos(self) -> List[EventoBase]:
        try:
            with Client() as client:
                url = f"{self.pgsql_url}/eventos"
                response = client.get(url)
                response.raise_for_status()
                return [EventoBase(**evento) for evento in response.json()]
        except Exception as e:
            logger.error(msg=f"Erro ao listar eventos: {str(e)}",ThreadName="ApiService",Application="pgsqlops", exc_info=True) # type: ignore


    def eventos_pendentes(self) -> List[EventoBase]:
        try:
            with Client() as client:
                url = f"{self.pgsql_url}/eventos/pendentes"
                response = client.get(url)
                response.raise_for_status()
                if response.status_code == 200:
                    return [EventoBase(**evento) for evento in response.json()]
                else:
                    logger.info(msg=f"Nenhum evento pendente encontrado: {response.status_code} - {response.text}",ThreadName="ApiService",Application="pgsqlops", exc_info=True) # type: ignore
                    return []
        except Exception as e:
            logger.error(msg=f"Erro ao listar eventos pendentes: {str(e)}",ThreadName="ApiService",Application="pgsqlops", exc_info=True) # type: ignore
            return []


    def procura_evento(self, evento_id: int) -> EventoBase:
        try:
            with Client() as client:
                url = f"{self.pgsql_url}/eventos/{evento_id}"
                response = client.get(url)
                response.raise_for_status()
                return EventoBase(**response.json())
        except Exception as e:
            logger.error(msg=f"Erro ao procurar evento: {str(e)}",ThreadName="ApiService",Application="pgsqlops", exc_info=True) # type: ignore

    def update_evento(self, evento_id:int, evento: EventoUpdate) -> EventoBase:
        try:
            with Client() as client:
                url = f"{self.pgsql_url}/eventos/{evento_id}"
                response = client.patch(url, content=evento.model_dump_json(exclude_none=True), headers={"Content-Type": "application/json"})
                response.raise_for_status()
                if response.status_code != 200:
                    logger.error(msg=f"Erro ao atualizar evento: {response.status_code} - {response.text}",ThreadName="ApiService",Application="pgsqlops", exc_info=True) # type: ignore
                return EventoBase(**response.json()) if response.status_code == 200 else response.json()
        except Exception as e:
            logger.error(msg=f"Erro ao atualizar evento: {str(e)}",ThreadName="ApiService",Application="pgsqlops", exc_info=True) # type: ignore


################################################################################### COMPROVA PADROES ########################################################################################
    def get_comprova_padroes(self) -> List[ComprovaPadraoBase]:
        try:
            with Client() as client:
                url = f"{self.pgsql_url}/comprova_padroes"
                response = client.get(url)
                response.raise_for_status()
                return [ComprovaPadraoBase(**padrao) for padrao in response.json()]
        except Exception as e:
            logger.error(msg=f"Erro ao listar comprova padroes: {str(e)}",ThreadName="ApiService",Application="pgsqlops", exc_info=True) # type: ignore


    def procura_comprova_padrao(self, comprova_padrao_id: int) -> Optional[ComprovaPadraoBase]:
        try:
            with Client() as client:
                url = f"{self.pgsql_url}/comprova_padroes/{comprova_padrao_id}"
                response = client.get(url)
                response.raise_for_status()
                return ComprovaPadraoBase(**response.json())
        except Exception as e:
            logger.error(msg=f"Erro ao procurar comprova padrao: {str(e)}",ThreadName="ApiService",Application="pgsqlops", exc_info=True) # type: ignore
            return None


    def create_comprova_padrao(self, comprova_padrao: ComprovaPadraoCreate) -> ComprovaPadraoBase:
        try:
            with Client() as client:
                url = f"{self.pgsql_url}/comprova_padroes"
                response = client.post(url, content=comprova_padrao.model_dump_json(exclude_none=True), headers={"Content-Type": "application/json"})
                response.raise_for_status()
                if response.status_code != 200:
                    logger.error(msg=f"Erro ao criar comprova padrao: {response.status_code} - {response.text}",ThreadName="ApiService",Application="pgsqlops", exc_info=True) # type: ignore
                return ComprovaPadraoBase(**response.json()) if response.status_code == 200 else response.json()
        except Exception as e:
            logger.error(msg=f"Erro ao criar comprova padrao: {str(e)}",ThreadName="ApiService",Application="pgsqlops", exc_info=True) # type: ignore
            return None


    def update_comprova_padrao(self, comprova_padrao_id:int, comprova_padrao: ComprovaPadraoUpdate) -> ComprovaPadraoBase:
        try:
            with Client() as client:
                url = f"{self.pgsql_url}/comprova_padroes/{comprova_padrao_id}"
                response = client.patch(url, content=comprova_padrao.model_dump_json(exclude_none=True), headers={"Content-Type": "application/json"})
                response.raise_for_status()
                if response.status_code != 200:
                    logger.error(msg=f"Erro ao atualizar comprova padrao: {response.status_code} - {response.text}",ThreadName="ApiService",Application="pgsqlops", exc_info=True) # type: ignore
                return ComprovaPadraoBase(**response.json()) if response.status_code == 200 else response.json()
        except Exception as e:
            logger.error(msg=f"Erro ao atualizar comprova padrao: {str(e)}",ThreadName="ApiService",Application="pgsqlops", exc_info=True) # type: ignore
            return None


################################################################################### CONTAS ########################################################################################
    def get_contas(self) -> List[ContaBase]:
        try:
            with Client() as client:
                url = f"{self.pgsql_url}/contas"
                response = client.get(url)
                response.raise_for_status()
                return [ContaBase(**conta) for conta in response.json()]
        except Exception as e:
            logger.error(msg=f"Erro ao listar contas: {str(e)}",ThreadName="ApiService",Application="pgsqlops", exc_info=True) # type: ignore


    def procura_conta(self, conta_id: int) -> Optional[ContaBase]:
        try:
            with Client() as client:
                url = f"{self.pgsql_url}/contas/{conta_id}"
                response = client.get(url)
                response.raise_for_status()
                return ContaBase(**response.json())
        except Exception as e:
            logger.error(msg=f"Erro ao procurar conta: {str(e)}",ThreadName="ApiService",Application="pgsqlops", exc_info=True) # type: ignore



    def create_conta(self, conta: ContaCreate) -> ContaBase:
        try:
            with Client() as client:
                url = f"{self.pgsql_url}/contas"
                response = client.post(url, content=conta.model_dump_json(exclude_none=True), headers={"Content-Type": "application/json"})
                response.raise_for_status()
                if response.status_code != 200:
                    logger.error(msg=f"Erro ao criar conta: {response.status_code} - {response.text}",ThreadName="ApiService",Application="pgsqlops", exc_info=True) # type: ignore
                return ContaBase(**response.json()) if response.status_code == 200 else response.json()
        except Exception as e:
            logger.error(msg=f"Erro ao criar conta: {str(e)}",ThreadName="ApiService",Application="pgsqlops", exc_info=True) # type: ignore
            return None


    def update_conta(self, conta_id:int, conta: ContaUpdate) -> ContaBase:
        try:
            with Client() as client:
                url = f"{self.pgsql_url}/contas/{conta_id}"
                response = client.patch(url, content=conta.model_dump_json(exclude_none=True), headers={"Content-Type": "application/json"})
                response.raise_for_status()
                if response.status_code != 200:
                    logger.error(msg=f"Erro ao atualizar conta: {response.status_code} - {response.text}",ThreadName="ApiService",Application="pgsqlops", exc_info=True) # type: ignore
                return ContaBase(**response.json()) if response.status_code == 200 else response.json()
        except Exception as e:
            logger.error(msg=f"Erro ao atualizar conta: {str(e)}",ThreadName="ApiService",Application="pgsqlops", exc_info=True) # type: ignore
            return None

################################################################################### FICHEIROS ########################################################################################

    def get_ficheiros(self) -> List[FicheiroBase]:
        try:
            with Client() as client:
                url = f"{self.pgsql_url}/ficheiros"
                response = client.get(url)
                response.raise_for_status()
                return [FicheiroBase(**ficheiro) for ficheiro in response.json()]
        except Exception as e:
            logger.error(msg=f"Erro ao listar ficheiros: {str(e)}",ThreadName="ApiService",Application="pgsqlops", exc_info=True) # type: ignore



    def procura_ficheiro(self, ficheiro_id: int) -> Optional[FicheiroBase]:
        try:
            with Client() as client:
                url = f"{self.pgsql_url}/ficheiros/{ficheiro_id}"
                response = client.get(url)
                response.raise_for_status()
                return FicheiroBase(**response.json())
        except Exception as e:
            logger.error(msg=f"Erro ao procurar ficheiro: {str(e)}",ThreadName="ApiService",Application="pgsqlops", exc_info=True) # type: ignore


    def create_ficheiro(self, ficheiro: FicheiroCreate) -> FicheiroBase:
        try:
            with Client() as client:
                url = f"{self.pgsql_url}/ficheiros"
                response = client.post(url, content=ficheiro.model_dump_json(exclude_none=True), headers={"Content-Type": "application/json"})
                response.raise_for_status()
                if response.status_code != 200:
                    logger.error(msg=f"Erro ao criar ficheiro: {response.status_code} - {response.text}",ThreadName="ApiService",Application="pgsqlops", exc_info=True) # type: ignore
                return FicheiroBase(**response.json()) if response.status_code == 200 else response.json()
        except Exception as e:
            logger.error(msg=f"Erro ao criar ficheiro: {str(e)}",ThreadName="ApiService",Application="pgsqlops", exc_info=True) # type: ignore
            return None
        
    
    def update_ficheiro(self, ficheiro_id:int, ficheiro: FicheiroUpdate) -> FicheiroBase:
        try:
            with Client() as client:
                url = f"{self.pgsql_url}/ficheiros/{ficheiro_id}"
                response = client.patch(url, content=ficheiro.model_dump_json(exclude_none=True), headers={"Content-Type": "application/json"})
                response.raise_for_status()
                if response.status_code != 200:
                    logger.error(msg=f"Erro ao atualizar ficheiro: {response.status_code} - {response.text}",ThreadName="ApiService",Application="pgsqlops", exc_info=True) # type: ignore
                return FicheiroBase(**response.json()) if response.status_code == 200 else response.json()
        except Exception as e:
            logger.error(msg=f"Erro ao atualizar ficheiro: {str(e)}",ThreadName="ApiService",Application="pgsqlops", exc_info=True) # type: ignore
            return None


################################################################################### FATURAS ########################################################################################

    def create_fatura(self, cabec: FaturaCabecCreate, linhas: List[FaturaLinhasCreate]):
        try:
            cabec_base = None
            linhas_base = []
            with Client() as client:
                url_cabec = f"{self.pgsql_url}/faturas_cabec"
                url_linhas = f"{self.pgsql_url}/faturas_linhas"
                
                response_cabec = client.post(url_cabec, content=cabec.model_dump_json(exclude_none=True), headers={"Content-Type": "application/json"})
                cabec_base = FaturaCabecBase(**response_cabec.json()) if response_cabec.status_code == 200 else response_cabec.json()
                
                for linha in linhas:
                    linha.fatura_id = cabec_base.id
                    response_linha = client.post(url_linhas, content=linha.model_dump_json(exclude_none=True), headers={"Content-Type": "application/json"})
                    response_linha.raise_for_status()
                    linhas_base.append(FaturaLinhasBase(**response_linha.json()))
                return cabec_base, linhas_base
        except Exception as e:
            logger.error(msg=f"Erro ao criar fatura: {str(e)}",ThreadName="ApiService",Application="pgsqlops", exc_info=True) # type: ignore
            return None



################################################################################### TABELA GERAL ########################################################################################

    def update_tabela(self, tabela: str, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        try:
            with Client() as client:
                url = f"{self.pgsql_url}/{tabela}/{data['id']}"
                response = client.patch(url, content=data.model_dump_json(exclude_none=True), headers={"Content-Type": "application/json"})
                response.raise_for_status()
                if response.status_code != 200:
                    logger.error(msg=f"Erro ao atualizar {tabela}: {response.status_code} - {response.text}",ThreadName="ApiService",Application="pgsqlops", exc_info=True) # type: ignore
                return response
        except Exception as e:
            logger.error(msg=f"Erro ao atualizar {tabela}: {str(e)}",ThreadName="ApiService",Application="pgsqlops", exc_info=True) # type: ignore
            return None