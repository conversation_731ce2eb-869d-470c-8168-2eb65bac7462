from pydantic import field_validator
from sqlmodel import SQLModel, <PERSON>
from typing import Optional
from datetime import datetime
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy import Column, text, Boolean, Integer, DateTime, String
from uuid import UUID


class Evento(SQLModel, table=True):
    __tablename__ = "eventos"
    id: int = Field(primary_key=True, sa_column_kwargs={"autoincrement": True})
    id_chave: UUID = Field(sa_column=Column(PG_UUID(as_uuid=True), nullable=False, unique=True, server_default=text("gen_random_uuid()")))
    tipo: str = Field(sa_column=Column(type_=String, nullable=False))
    tabela: str = Field(sa_column=Column(type_=String, nullable=False))
    fkid: int = Field(sa_column=Column(type_=Integer, nullable=False))
    fkid_chave: UUID = Field(sa_column=Column(PG_UUID(as_uuid=True), nullable=False))
    estado: int = Field(sa_column=Column(type_=Integer, index=True, server_default=text("0")))
    obs: str = Field(sa_column=Column(type_=String, nullable=True))
    data_criacao: datetime = Field(sa_column=Column(type_=DateTime, nullable=False, server_default=text("now()")))
    data_alteracao: datetime = Field(sa_column=Column(type_=DateTime, nullable=False, server_default=text("now()")))


class EventoUpdate(SQLModel):
    estado: int = Field(default=None)
    obs: Optional[str] = Field(default=None)


class EventoBase(SQLModel):
    id: int = Field(default=None)
    id_chave: UUID = Field(default=None)
    tipo: str = Field(default=None)
    tabela: str = Field(default=None)
    fkid: int = Field(default=None)
    fkid_chave: UUID = Field(default=None)
    estado: int = Field(default=None)
    obs: Optional[str] = Field(default=None)
    data_criacao: datetime = Field(default=None)
    data_alteracao: datetime = Field(default=None)
