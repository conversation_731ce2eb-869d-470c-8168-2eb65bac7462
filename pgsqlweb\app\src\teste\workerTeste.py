from src.eventos.classEvento import Evento
from src.services.api import api_update

class WorkerTeste:
    def processar_evento(self, evento: Evento):
        try:
            if evento.tipo == 'INSERT':
                response = api_update(tabela="teste", data={"campo3": 'TIPO INSERT - WorkerTeste', "id": evento.fkid})
                if response.status_code == 200:
                    self.atualizar_estado(evento=evento, estado=200)
                else:
                    self.atualizar_estado(evento=evento, estado=99, obs="update falhou")
            elif evento.tipo == 'UPDATE':
                self.atualizar_estado(evento=evento, estado=100, obs="evento UPDATE não configurado")

            else:
                self.atualizar_estado(evento=evento, estado=100, obs="tipo não configurado")
        except Exception as e:
            self.atualizar_estado(evento=evento, estado=99, obs='erro_worker_teste: '+ str(e))

    def atualizar_estado(self, evento, estado, obs=""):
        api_update(tabela="eventos", data={"estado": estado, "obs": obs, "id_chave": evento.id_chave})