{"cells": [{"cell_type": "code", "execution_count": 103, "id": "acb97ba9", "metadata": {}, "outputs": [], "source": ["from pgsqlops.app.services.api_service import ApiService\n", "from comum.services.logger import setup_logger\n", "logger = setup_logger()\n", "api_service = ApiService()\n", "nao_processados = api_service.get_unprocessed_documents()\n", "texto = nao_processados.results[1].content\n", "# print(texto)\n"]}, {"cell_type": "code", "execution_count": 104, "id": "c38c0ee0", "metadata": {}, "outputs": [], "source": ["import re\n", "from datetime import datetime\n", "from comum.models.faturas_cabec import FaturaCabecCreate\n", "from comum.models.faturas_linhas import FaturaLinhasCreate\n", "from comum.services.logger import setup_logger\n", "logger = setup_logger()\n", "\n", "class FaturaBox111Extractor:\n", "    def __init__(self, texto, nome_ficheiro):\n", "        self.texto = texto\n", "        self.nome_ficheiro = nome_ficheiro\n", "        self.cabec = FaturaCabecCreate()\n", "        self.linhas = [FaturaLinhasCreate()]\n", "\n", "\n", "\n", "    def extrair_fatura(self) -> str:\n", "        padrao = r'F\\s*a\\s*c\\s*t\\s*u\\s*r\\s*a\\s*\\s*-\\s*R\\s*e\\s*c\\s*i\\s*b\\s*o\\s*([\\w\\d]+)\\s*/\\s*(\\d{3,6})'\n", "        match = re.search(pad<PERSON><PERSON>, self.texto, re.IGNORECASE)\n", "        if match:\n", "            serie = match.group(1)\n", "            numero = match.group(2)\n", "            return f'FAT:{serie}/{numero}'\n", "        return None\n", "\n", "    def extrair_nif(self) -> str:\n", "        padrao = r'N\\s*I\\s*F\\s*:\\s*(\\d{9})'\n", "        match = re.search(pad<PERSON><PERSON>, self.texto, re.IGNORECASE)\n", "        if match:\n", "            return match.group(1)\n", "\n", "        return None\n", "    def extrair_nif_entidade(self) -> str:\n", "        padrao = r'N\\s*º\\s*C\\s*o\\s*n\\s*t\\s*r\\s*i\\s*b\\s*u\\s*i\\s*n\\s*t\\s*e\\s*:\\s*(\\d{9})'\n", "        match = re.search(pad<PERSON><PERSON>, self.texto, re.IGNORECASE)\n", "        if match:\n", "            return match.group(1)\n", "        return None\n", "\n", "    def extrair_base(self) -> float:\n", "        padrao = r'T otal ilíquido:[:\\s]*([\\d,.]+)€'\n", "        match = re.search(padrao, self.texto)\n", "        return float(match.group(1).replace(',', '.')) if match else 0.0\n", "\n", "    def extrair_desconto(self) -> float:\n", "        padrao = r'T otal descontos:[:\\s]*([\\d,.]+)€'\n", "        match = re.search(padrao, self.texto)\n", "        return float(match.group(1).replace(',', '.')) if match else 0.0\n", "\n", "\n", "    def extrair_iva(self) -> float:\n", "        padrao = r'T otal IV A :[:\\s]*([\\d,.]+)€'\n", "        match = re.search(padrao, self.texto)\n", "        return float(match.group(1).replace(',', '.')) if match else 0.0\n", "\n", "    def extrair_valor(self) -> float:\n", "        padrao = r'Totaldo D ocum ento:[:\\s]*([\\d,.]+)€'\n", "        match = re.search(padrao, self.texto)\n", "        return float(match.group(1).replace(',', '.')) if match else 0.0\n", "    \n", "    def extrair_data(self) -> datetime:\n", "        padrao_data = r'D\\s*a\\s*t\\s*a\\s*:\\s*(\\d{4}-\\d{2}-\\d{2})'\n", "        padrao_hora = r'H\\s*o\\s*r\\s*a\\s*:\\s*(\\d{2}:\\d{2}:\\d{2})'\n", "        \n", "        match_data = re.search(padrao_data, self.texto, re.IGNORECASE)\n", "        match_hora = re.search(padra<PERSON>_hora, self.texto, re.IGNORECASE)\n", "\n", "        if match_data and match_hora:\n", "            data_str = match_data.group(1)\n", "            hora_str = match_hora.group(1)\n", "            data = datetime.strptime(data_str, '%Y-%m-%d').date()\n", "            hora = datetime.strptime(hora_str, '%H:%M:%S').time()\n", "            return datetime.combine(data, hora)\n", "\n", "        return datetime(1900, 1, 1, 0, 0, 0)\n", "\n", "    def extra<PERSON>_lin<PERSON>(self):\n", "        padrao = re.compile(\n", "                                r\"\"\"\n", "                                (?P<artigo>.+?)         # artigo (tudo até antes dos números)\n", "                                \\s+(?P<quantidade>\\d+),            # quantidade (ex: 1)\n", "                                \\s+(?P<desconto>\\d+,\\d+)%          # desconto (ex: 0,00%)\n", "                                \\s+(?P<tx_iva>\\d+)%                # taxa de IVA (ex: 23%)\n", "                                \\s+(?P<valor_unitario>\\d+,\\d+)€    # valor unitário (ex: 22,68€)\n", "                                \\s+(?P<valor>\\d+,\\d+)€             # total (ex: 27,90€)\n", "                                \"\"\",\n", "                                re.VERBOSE\n", "                            )\n", "        linhas = []\n", "        matches = padrao.finditer(self.texto)\n", "        \n", "        for match in matches:\n", "            linha = FaturaLinhasCreate()\n", "            linha.artigo = match.group('artigo').strip()\n", "            linha.artigo = re.sub(r'(?<=\\w)\\s(?=\\w)', '', linha.artigo)\n", "            linha.artigo = re.sub(r'\\s{2,}', ' ', linha.artigo)\n", "            linha.desconto = float(match.group('desconto').replace(',', '.'))\n", "            linha.tx_iva = float(match.group('tx_iva')) / 100\n", "            linha.base = float(match.group('valor_unitario').replace(',', '.'))\n", "            linha.valor = float(match.group('valor').replace(',', '.'))\n", "            linha.valor_bruto = round(linha.base + linha.desconto, 2)\n", "            linha.iva = round(linha.valor - linha.base, 2)\n", "            linhas.append(linha)\n", "        \n", "        return linhas if linhas else [FaturaLinhasCreate()]\n", "\n", "        \n", "\n", "\n", "    def run_extraction(self):\n", "        self.cabec.fatura = self.extrair_fatura()\n", "        self.cabec.nif = self.extrair_nif()\n", "        self.cabec.nif_entidade = self.extrair_nif_entidade()\n", "        self.cabec.base = self.extrair_base()\n", "        self.cabec.desconto = self.extrair_desconto()\n", "        self.cabec.iva = self.extrair_iva()\n", "        self.cabec.valor = self.extrair_valor()\n", "        self.cabec.valor_bruto = round(self.cabec.base + self.cabec.desconto, 2)\n", "        self.cabec.data = self.extrair_data()\n", "        self.cabec.ano = self.cabec.data.year\n", "        self.cabec.mes = self.cabec.data.month\n", "        self.cabec.entidade_id = 34\n", "\n", "\n", "        self.linhas = self.extrair_linhas()\n", "        for linha in self.linhas:\n", "            linha.data = self.cabec.data\n", "            linha.ano = self.cabec.ano\n", "            linha.mes = self.cabec.mes\n", "            linha.fatura = self.cabec.fatura\n", "            \n", "\n", "        return self.cab<PERSON>, self.linhas\n", "        \n", "\n", "\n", "\n", "\n", "cabec, linhas = FaturaBox111Extractor(texto=texto, nome_ficheiro='teste').run_extraction()\n", "\n"]}, {"cell_type": "code", "execution_count": 105, "id": "9ce64dcb", "metadata": {}, "outputs": [{"data": {"text/plain": ["FaturaCabecCreate(fatura='FAT:2025B/6535', nif='244494843', nif_entidade='514271094', base=78.33, iva=5.41, desconto=39.8, valor=43.95, valor_bruto=118.13, entidade_id=34, link=None, data=datetime.datetime(2025, 5, 7, 17, 52, 12), ano=2025, mes=5, ativo=True)"]}, "execution_count": 105, "metadata": {}, "output_type": "execute_result"}], "source": ["cabec"]}, {"cell_type": "code", "execution_count": 106, "id": "e26c4915", "metadata": {}, "outputs": [{"data": {"text/plain": ["[FaturaLinhasCreate(fatura_id=0, fatura='FAT:2025B/6535', artigo='DV183602Pag (IníciodeTransf.) Seguro', base=15.0, iva=0.0, desconto=0.0, valor=15.0, valor_bruto=15.0, tx_iva=0.0, data=datetime.datetime(2025, 5, 7, 17, 52, 12), ano=2025, mes=5, ativo=True),\n", " FaturaLinhasCreate(fatura_id=0, fatura='FAT:2025B/6535', artigo='DV183604 (28/04/2025) AcessoLivre - Fid - Quinzenal2025', base=22.68, iva=-8.73, desconto=50.0, valor=13.95, valor_bruto=72.68, tx_iva=0.23, data=datetime.datetime(2025, 5, 7, 17, 52, 12), ano=2025, mes=5, ativo=True),\n", " FaturaLinhasCreate(fatura_id=0, fatura='FAT:2025B/6535', artigo='DV183603 (PagamentoPontual) Inscrição', base=40.65, iva=-25.65, desconto=70.0, valor=15.0, valor_bruto=110.65, tx_iva=0.23, data=datetime.datetime(2025, 5, 7, 17, 52, 12), ano=2025, mes=5, ativo=True)]"]}, "execution_count": 106, "metadata": {}, "output_type": "execute_result"}], "source": ["linhas"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.0"}}, "nbformat": 4, "nbformat_minor": 5}