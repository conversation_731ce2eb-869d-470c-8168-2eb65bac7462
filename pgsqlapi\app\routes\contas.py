from typing import List
from sqlmodel import Session
from fastapi import APIRouter, Depends
from comum.models.contas import ContaBase, ContaCreate, ContaUpdate
from app.database.postgres import get_session
from app.workers.contas import ContasWorker


contas_router = APIRouter()

def get_worker(session: Session = Depends(get_session)) -> ContasWorker:
    return ContasWorker(session)


@contas_router.get("/contas", response_model=List[ContaBase])
async def lista_completa(worker: ContasWorker = Depends(get_worker)):
    return list(worker.lista())


@contas_router.get("/contas/{id}", response_model=ContaBase)
async def procura(id: int, worker: ContasWorker = Depends(get_worker)):
    return worker.procura(id)


@contas_router.post("/contas", response_model=ContaBase)
async def cria(novos_dados: ContaCreate, worker: ContasWorker = Depends(get_worker)):
    return worker.cria(novos_dados)


@contas_router.patch("/contas/{id}", response_model=ContaBase)
async def atualiza(id: int, dados: ContaUpdate, worker: ContasWorker = Depends(get_worker)):
    return worker.atualiza(id, dados)
    

@contas_router.delete("/contas/{id}")
async def remove(id: int, worker: ContasWorker = Depends(get_worker)):
    worker.remove(id)
    return {"message": f"ID: **{id}** deleted!"}