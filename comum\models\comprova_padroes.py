from sqlmodel import SQLModel, Field, Relationship
from typing import Optional
from datetime import datetime
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy import Column, text, Boolean, DateTime, String
from uuid import UUID


class ComprovaPadrao(SQLModel, table=True):
    __tablename__ = "comprova_padroes"
    id: int = Field(primary_key=True, sa_column_kwargs={"autoincrement": True})
    id_chave: UUID = Field(sa_column=Column(PG_UUID(as_uuid=True), nullable=False, unique=True, server_default=text("gen_random_uuid()")))
    padrao: str = Field(sa_column=Column(type_=String, nullable=False, unique=True))
    segmento_id: int = Field(foreign_key="segmentos.id")
    entidade_id: int = Field(foreign_key="entidades.id")
    ativo: bool = Field(sa_column=Column(Boolean, nullable=False, server_default=text("true")))
    data_criacao: datetime = Field(sa_column=Column(type_=DateTime, nullable=False, server_default=text("now()")))
    data_alteracao: datetime = Field(sa_column=Column(type_=DateTime, nullable=False, server_default=text("now()")))

    entidade_rel: Optional["Entidade"] = Relationship(back_populates="comprova_padroes_rel") # type: ignore
    segmento_rel: Optional["Segmento"] = Relationship(back_populates="comprova_padroes_rel") # type: ignore

class ComprovaPadraoCreate(SQLModel):
    padrao: str
    segmento_id: int
    entidade_id: int
    ativo: Optional[bool] = Field(default=True)

    class Config:
        json_schema_extra = {
            "example": {
                "padrao": "teste_padrao",
                "segmento_id": 0,
                "entidade_id": 0,
                "ativo": True
            }
        }


class ComprovaPadraoUpdate(SQLModel):
    padrao: Optional[str] = Field(default=None)
    segmento_id: Optional[int] = Field(default=None)
    entidade_id: Optional[int] = Field(default=None)
    ativo: Optional[bool] = Field(default=None)


class ComprovaPadraoBase(SQLModel):
    id: int = Field(default=None)
    id_chave: UUID = Field(default=None)
    padrao: str = Field(default=None)
    segmento_id: Optional[int] = Field(default=None)
    entidade_id: Optional[int] = Field(default=None)
    ativo: bool = Field(default=None)
    data_criacao: datetime = Field(default=None)
    data_alteracao: datetime = Field(default=None)
