import io
import zipfile
from src.services.api import api_create
import base64
from src.services.logger import setup_logger
logger = setup_logger()


class WorkerRecibos:
    def __init__(self):
        self.tabela = "recibos"
        self.tabela_eventos = "eventos"

    def _enviar_ficheiro(self, ficheiro, id_chave):
        try:
            if hasattr(ficheiro, 'read'):
                file_bin = ficheiro.read()
                if hasattr(ficheiro, 'seek'):
                    ficheiro.seek(0) 
            else:
                with open(ficheiro, 'rb') as f:
                    file_bin = f.read()

            if not file_bin:
                raise ValueError("O conteúdo do ficheiro está vazio.")

            pdf_b64 = base64.b64encode(file_bin).decode('utf-8')

            base64.b64decode(pdf_b64) 

            payload = {
                "tabela": "recibos",
                "fkid_chave": str(id_chave),
                "binario": pdf_b64,
                "ativo": True
            }

            api_create(tabela="ficheiros", data=payload)
        except Exception as e:
            message = {
                'id_chave': id_chave,
            }
            logger.error(f"Erro ao enviar ficheiro", extra={"message": message}, ThreadName="WorkerRecibos._enviar_ficheiro")

    def processar_ficheiro(self, ficheiro, utilizador, save=False):
        try:
            if utilizador == "ricardo":
                from src.recibos.extractorEmpifarma import ReciboExtractor
                extraction = ReciboExtractor(pdf=ficheiro).run_extraction()
                if save:
                    response = api_create(tabela='recibos', data=extraction)
                    if response.status_code == 200:
                        recibo = response.json()
                        if hasattr(ficheiro, "seek"):
                            ficheiro.seek(0) 
                        self._enviar_ficheiro(ficheiro, recibo['id_chave'])
                        return response
                return extraction
            else:
                raise Exception(f"utilizador:{utilizador} não implementado")
                return None
        except Exception as e:
            return f"Erro: {e}"   

    @staticmethod
    def create_zip(files):
        zip_buffer = io.BytesIO()
        with zipfile.ZipFile(zip_buffer, "w") as zf:
            for nome_arquivo, conteudo in files:
                zf.writestr(nome_arquivo, conteudo)
        zip_buffer.seek(0)
        return zip_buffer.getvalue()

