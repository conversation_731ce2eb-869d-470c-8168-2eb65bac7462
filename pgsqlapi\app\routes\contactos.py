from typing import List
from sqlmodel import Session
from fastapi import APIRouter, Depends
from comum.models.contactos import ContactoBase, ContactoCreate, ContactoUpdate
from app.database.postgres import get_session
from app.workers.contactos import ContactosWorker


contactos_router = APIRouter()

def get_worker(session: Session = Depends(get_session)) -> ContactosWorker:
    return ContactosWorker(session)


@contactos_router.get("/contactos", response_model=List[ContactoBase])
async def lista_completa(worker: ContactosWorker = Depends(get_worker)):
    return list(worker.lista())


@contactos_router.get("/contactos/{id}", response_model=ContactoBase)
async def procura(id: int, worker: ContactosWorker = Depends(get_worker)):
    return worker.procura(id)


@contactos_router.post("/contactos", response_model=ContactoBase)
async def cria(novos_dados: ContactoCreate, worker: ContactosWorker = Depends(get_worker)):
    return worker.cria(novos_dados)


@contactos_router.patch("/contactos/{id}", response_model=ContactoBase)
async def atualiza(id: int, dados: ContactoUpdate, worker: ContactosWorker = Depends(get_worker)):
    return worker.atualiza(id, dados)
    

@contactos_router.delete("/contactos/{id}")
async def remove(id: int, worker: ContactosWorker = Depends(get_worker)):
    worker.remove(id)
    return {"message": f"ID: **{id}** deleted!"}