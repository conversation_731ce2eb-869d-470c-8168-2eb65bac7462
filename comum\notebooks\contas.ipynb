{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Conta(id=1, id_chave=UUID('d8c02094-fc49-4329-b464-2e9e60c9231c'), conta='0507021920600', utilizador='ricardo', entidade_id=18, ativo=True, data_criacao=datetime.datetime(2025, 5, 4, 15, 59, 49, 506991), data_alteracao=datetime.datetime(2025, 5, 4, 15, 59, 49, 506991)),\n", " Conta(id=2, id_chave=UUID('cc1b1f5c-3ad7-4d24-a7cb-791a6c54076c'), conta='0507021920261', utilizador='ricardo', entidade_id=18, ativo=True, data_criacao=datetime.datetime(2025, 5, 4, 15, 59, 49, 506991), data_alteracao=datetime.datetime(2025, 5, 4, 15, 59, 49, 506991))]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from src.services.api import api_get\n", "from src.contas.classConta import Conta\n", "\n", "contas = api_get(tabela='contas')\n", "contas = [Conta(**conta) for conta in contas.json()]\n", "contas_ricardo = [conta for conta in contas if conta.utilizador == 'ricardo']\n", "contas_ricardo\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.0"}}, "nbformat": 4, "nbformat_minor": 2}