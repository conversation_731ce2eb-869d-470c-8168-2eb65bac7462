import streamlit as st
from src.services.supabase_client import connect
from src.entidades.classEntidade import Entidade
from src.segmentos.classSegmento import Segmento
from src.comprovativos.classComprovativo import ComprovativoPadrao
from src.contas.classConta import Conta

supabase = connect()
def get_entidades() -> list[Entidade]:
    entidades = supabase.table('entidades').select('*').order('id_chave', desc=False).execute().data 
    entidades = [Entidade(**entidade) for entidade in entidades]
    return entidades


def get_padroes() -> list[ComprovativoPadrao]:
    padroes = supabase.table('comprova_padroes').select('*').order('id_chave', desc=False).execute().data 
    padroes = [ComprovativoPadrao(**padrao) for padrao in padroes]
    return padroes



def get_segmentos() -> list[Segmento]:
    segmentos = supabase.table('segmentos').select('*').order('segmento', desc=False).execute().data 
    segmentos = [Segmento(**segmento) for segmento in segmentos]
    return segmentos


def get_contas() -> list[Conta]:
    contas = supabase.table('contas').select('*').order('conta', desc=False).execute().data 
    contas = [Conta(**conta) for conta in contas]
    return contas


segmentos = get_segmentos()
entidades = get_entidades()
contas = get_contas()

categorias = list(set(segmento.categoria for segmento in segmentos))
utilizadores = list(set(conta.utilizador for conta in contas))

def main():
    st.subheader("INSERIR")
    tab1, tab2, tab3, tab4 = st.tabs(["novo_segmento", "nova_entidade", "novo_padrao", "nova_conta"])


############################################## SEGMENTOS #################################################
    with tab1:
        segmento = st.text_input("segmento")
        categoria = st.selectbox("categoria", categorias, index=4)
        with st.form(key="form_comprov_segmentos"):
            if st.form_submit_button("Salvar"):
                existing_segmento = supabase.table("segmentos").select("*").eq("segmento", segmento).execute().data
                if existing_segmento:
                    st.error("segmento ja existe")
                else:
                    response = supabase.table("segmentos").insert({"segmento": segmento, "categoria": categoria}).execute()
                    if response.data:
                        st.success("saved")
        segmentos_dados = [segmento.__dict__ for segmento in get_segmentos()]
        st.dataframe(segmentos_dados, use_container_width=True)

############################################## ENTIDADES #################################################
    with tab2:
        try:
            with st.form(key="form_comprov_entidades"):
                entidade = st.text_input("entidade")
                if st.form_submit_button("Salvar"):
                    existing_entidade = supabase.table("entidades").select("*").eq("entidade", entidade).execute().data
                    if existing_entidade:
                        st.error("entidade ja existe")
                    else:
                        response = supabase.table("entidades").insert({"entidade": entidade}).execute()
                        if response.data:
                            st.success("saved")
            entidades_dados = [entidade.__dict__ for entidade in get_entidades()]
            st.dataframe(entidades_dados, use_container_width=True)
        except Exception as e:
            st.error(f"Error: {e}")

############################################## PADROES #################################################
    with tab3:
        with st.form(key="form_comprov_padroes"):
            col1, col2, col3 = st.columns([1,1,1])
            index_null = next((i for i, segmento in enumerate(segmentos) if segmento.id_chave == 0), None)
            with col1:
                padrao = st.text_input("padrao")
            with col2:    
                segmento = st.selectbox(
                    "segmento",
                    options=segmentos,
                    format_func=lambda segmento: segmento.segmento,
                    index=index_null
                )
            with col3:
                entidade = st.selectbox(
                    "entidade",
                    options=entidades,
                    format_func=lambda entidade: entidade.entidade,
                )
            if st.form_submit_button("Salvar"):
                if segmento is None or entidade is None:
                    st.error("Campo segmento/entidade nao preenchido")
                else:
                    existing_padrao = supabase.table("comprova_padroes").select("*").eq("padrao", padrao).execute().data
                    if existing_padrao:
                        st.error("padrao ja existe")
                    else:
                        if segmento:
                            response = supabase.table("comprova_padroes").insert({"padrao": padrao, "segmento_id": segmento.id_chave}).execute()
                            if response.data:
                                st.success("saved")
                        if entidade:
                            response = supabase.table("comprova_padroes").insert({"padrao": padrao, "entidade_id": entidade.id_chave}).execute()
                            if response.data:
                                st.success("saved")
        padroes_dados = [padrao.__dict__ for padrao in get_padroes()]
        st.dataframe(padroes_dados, use_container_width=True)


############################################## CONTAS #################################################
    with tab4:
        with st.form(key="form_comprov_contas"):
            conta = st.text_input("conta")
            utilizador = st.selectbox(
                "utilizador",
                options=utilizadores,
                format_func=lambda utilizador: utilizador,
            )
            if st.form_submit_button("Salvar"):
                if conta == "" or utilizador == "":
                    st.error("conta/utilizador nao preenchido")
                else:
                    try:
                        response = supabase.table("contas").insert({"conta": conta, "utilizador": utilizador}).execute()
                        if response.data:
                            st.success("saved")
                    except Exception as e:
                        st.error(f"Error: {e}")
        contas_dados = [conta.__dict__ for conta in get_contas()]
        st.dataframe(contas_dados, use_container_width=True)
            



if __name__ == "__main__":
    main()