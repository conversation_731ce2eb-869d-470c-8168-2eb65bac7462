from typing import List
from comum.services.logger import setup_logger
from comum.models.eventos import EventoBase, EventoUpdate
from .api_service import ApiService
from app.handlers.comprovativos_handler import ComprovativosHandler

logger = setup_logger()

class EventosService:
    def __init__(self, api_service: ApiService):
        self.api_service = api_service


    def get_eventos_pendentes(self) -> List[EventoBase]:
        try:
            return self.api_service.eventos_pendentes()
        except Exception as e:
            logger.error(msg=f"Erro get_eventos_pendentes(): {str(e)}",ThreadName="EventosService",Application="pgsqlops", exc_info=True) # type: ignore

    def processar_evento(self, evento: EventoBase):
        try:
            if evento.tabela.lower() == 'teste':
                # self.worker_teste.processar_evento(evento)
                pass

            elif evento.tabela.lower() == 'comprovativos':
                ComprovativosHandler(api_service=self.api_service).processar_evento(evento)

            # elif evento.tabela == 'contactos':
            #     # self.worker_contacto.processar_evento(evento)
            #     self.set_estado(evento_id=evento.id, estado=100, obs="Contactos")
                
            else:
                self.api_service.update_evento(evento_id=evento.id, evento=EventoUpdate(estado=100, obs=f"Tabela: {evento.tabela} não configurada"))
        except Exception as e:
            logger.error(msg=f"Erro processar_evento(): {str(e)}",ThreadName="EventosService",Application="pgsqlops", exc_info=True) # type: ignore

