from sqlmodel import SQLModel, Field, Relationship
from sqlalchemy import Column, text, Boolean, DateTime, String
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from pydantic import field_validator
from typing import Optional, List
from datetime import datetime
from uuid import UUID


class Segmento(SQLModel, table=True):
    __tablename__ = "segmentos"
    id: int = Field(primary_key=True, sa_column_kwargs={"autoincrement": True})
    id_chave: UUID = Field(sa_column=Column(PG_UUID(as_uuid=True), unique=True, server_default=text("gen_random_uuid()")))
    segmento: str = Field(sa_column=Column(type_=String, nullable=False))
    categoria: str = Field(sa_column=Column(type_=String, nullable=False))
    icon: str = Field(sa_column=Column(type_=String, nullable=True))
    ativo: bool = Field(sa_column=Column(Boolean, nullable=False, server_default=text("true")))
    data_criacao: datetime = Field(sa_column=Column(type_=DateTime, nullable=False, server_default=text("now()")))
    data_alteracao: datetime = Field(sa_column=Column(type_=DateTime, nullable=False, server_default=text("now()")))

    comprovativos_rel: list["Comprovativo"] = Relationship(back_populates="segmento_rel") # type: ignore
    comprova_padroes_rel: Optional[List["ComprovaPadrao"]] = Relationship(back_populates="segmento_rel") # type: ignore

class SegmentoCreate(SQLModel):
    #id: Optional[int]
    segmento: str
    categoria: str
    icon: Optional[str] = Field(default=None)

    @field_validator("segmento", "categoria",mode="before")
    def lower(cls, valor: str):
        if valor is not None:
            return valor.lower()

    class Config:
        json_schema_extra = {
            "example": {
                "segmento": "teste_segmento",
                "categoria": "teste_categoria",
                "icon": "teste_icon",
                "ativo": True
            }
        }


class SegmentoUpdate(SQLModel):
    segmento: Optional[str] = Field(default=None)
    categoria: Optional[str] = Field(default=None)
    icon: Optional[str] = Field(default=None)
    ativo: Optional[bool] = Field(default=None)



class SegmentoBase(SQLModel):
    id: int = Field(default=None) 
    id_chave: UUID = Field(default=None)
    segmento: str = Field(default=None)
    categoria: str = Field(default=None)
    icon: Optional[str] = Field(default=None)
    ativo: bool = Field(default=None)
    data_criacao: datetime = Field(default=None)
    data_alteracao: datetime = Field(default=None)