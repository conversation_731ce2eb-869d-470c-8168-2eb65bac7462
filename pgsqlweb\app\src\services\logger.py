import logging
import seqlog
import sys

def setup_logger():
    seqlog.log_to_seq(
                        server_url="http://192.168.1.77:5341",
                        api_key="7gnEAaGAC9zWaYe6lKNh",
                        level=logging.INFO,
                        batch_size=10,
                        auto_flush_timeout=10,
                        override_root_logger=True,
                        json_encoder_class=None,
                        support_extra_properties=True,
                    )
    logger = logging.getLogger(__name__)

    # Captura de exceções globais
    def handle_exception(exc_type, exc_value, exc_traceback):
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        logger.exception("Uncaught exception", exc_info=(exc_type, exc_value, exc_traceback))

    sys.excepthook = handle_exception

    return logger