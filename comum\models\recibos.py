from sqlmodel import SQLModel, Field, Relationship
from typing import Optional
from datetime import datetime, date
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy import Column, text, Boolean, DateTime, String, Float, Integer, Date
from uuid import UUID


class Recibo(SQLModel, table=True):
    __tablename__ = "recibos"
    id: int = Field(primary_key=True, sa_column_kwargs={"autoincrement": True})
    id_chave: UUID = Field(sa_column=Column(PG_UUID(as_uuid=True), nullable=False, unique=True, server_default=text("gen_random_uuid()")))
    nome: str = Field(sa_column=Column(type_=String, nullable=True))
    categoria: str = Field(sa_column=Column(type_=String, nullable=True))
    id_beneficiario: str = Field(sa_column=Column(type_=String, nullable=True))
    departamento: str = Field(sa_column=Column(type_=String, nullable=True))
    seguro: str = Field(sa_column=Column(type_=String, nullable=True))
    id_colaborador: str = Field(sa_column=Column(type_=String, nullable=True))
    nif: str = Field(sa_column=Column(type_=String, nullable=True))
    link: str = Field(sa_column=Column(type_=String, nullable=True))
    vencimento_base: float = Field(sa_column=Column(type_=Float, nullable=True))
    vencimento_hora: float = Field(sa_column=Column(type_=Float, nullable=True))
    valor_liquido: float = Field(sa_column=Column(type_=Float, nullable=True))
    valor_bruto: float = Field(sa_column=Column(type_=Float, nullable=True))
    descontos_totais: float = Field(sa_column=Column(type_=Float, nullable=True))
    dias_trabalhados: int = Field(sa_column=Column(type_=Integer, nullable=True))
    data_fecho: date = Field(sa_column=Column(type_=Date, nullable=True))
    ano: int = Field(sa_column=Column(type_=Integer, nullable=True))
    mes: int = Field(sa_column=Column(type_=Integer, nullable=True))
    entidade_id: int = Field(foreign_key="entidades.id")
    ativo: bool = Field(sa_column=Column(Boolean, nullable=False, server_default=text("true")))
    data_criacao: datetime = Field(sa_column=Column(type_=DateTime, nullable=False, server_default=text("now()")))
    data_alteracao: datetime = Field(sa_column=Column(type_=DateTime, nullable=False, server_default=text("now()")))

    entidade_rel: Optional["Entidade"] = Relationship(back_populates="recibos_rel") # type: ignore


class ReciboCreate(SQLModel):
    nome: Optional[str] = Field(default=None)
    categoria: Optional[str] = Field(default=None)
    id_beneficiario: Optional[str] = Field(default=None)
    departamento: Optional[str] = Field(default=None)
    seguro: Optional[str] = Field(default=None)
    id_colaborador: Optional[str] = Field(default=None)
    nif: Optional[str] = Field(default=None)
    link: Optional[str] = Field(default=None)
    vencimento_base: Optional[float] = Field(default=0)
    vencimento_hora: Optional[float] = Field(default=0)
    valor_liquido: Optional[float] = Field(default=0)
    valor_bruto: Optional[float] = Field(default=0)
    descontos_totais: Optional[float] = Field(default=0)
    dias_trabalhados: Optional[int] = Field(default=0)
    data_fecho: Optional[date] = Field(default=None)
    ano: Optional[int] = Field(default=None)
    mes: Optional[int] = Field(default=None)
    entidade_id: Optional[int] = Field(default=0)
    ativo: Optional[bool] = Field(default=True)

    class Config:
        json_schema_extra = {
            "example": {
                "nome": "teste_nome",
                "categoria": "teste_categoria",
                "id_beneficiario": "teste_id_beneficiario",
                "departamento": "teste_departamento",
                "seguro": "teste_seguro",
                "id_colaborador": "teste_id_colaborador",
                "nif": "999999999",
                "link": "teste_link",
                "vencimento_base": 100.0,
                "vencimento_hora": 100.0,
                "valor_liquido": 100.0,
                "valor_bruto": 100.0,
                "descontos_totais": 100.0,
                "dias_trabalhados": 100,
                "data_fecho": "2000-01-01",
                "ano": 2000,
                "mes": 1,
                "entidade_id": 1,
                "ativo": True
            }
        }


class ReciboUpdate(SQLModel):
    nome: Optional[str] = Field(default=None)
    categoria: Optional[str] = Field(default=None)
    id_beneficiario: Optional[str] = Field(default=None)
    departamento: Optional[str] = Field(default=None)
    seguro: Optional[str] = Field(default=None)
    id_colaborador: Optional[str] = Field(default=None)
    nif: Optional[str] = Field(default=None)
    link: Optional[str] = Field(default=None)
    vencimento_base: Optional[float] = Field(default=None)
    vencimento_hora: Optional[float] = Field(default=None)
    valor_liquido: Optional[float] = Field(default=None)
    valor_bruto: Optional[float] = Field(default=None)
    descontos_totais: Optional[float] = Field(default=None)
    dias_trabalhados: Optional[int] = Field(default=None)
    data_fecho: Optional[date] = Field(default=None)
    entidade_id: Optional[int] = Field(default=None)
    ativo: Optional[bool] = Field(default=None)

    def to_dict(self) -> dict:
        """Converte o objeto para dicionário, tratando campos especiais como datas."""
        result = {}
        for key, value in self.__dict__.items():
            if value is None:
                continue
            if hasattr(value, 'isoformat'):  # Para campos date/datetime
                result[key] = value.isoformat()
            else:
                result[key] = value
        return result

class ReciboBase(SQLModel):
    id: int = Field(default=None)
    id_chave: UUID = Field(default=None)
    nome: Optional[str] = Field(default=None)
    categoria: Optional[str] = Field(default=None)
    id_beneficiario: Optional[str] = Field(default=None)
    departamento: Optional[str] = Field(default=None)
    seguro: Optional[str] = Field(default=None)
    id_colaborador: Optional[str] = Field(default=None)
    nif: Optional[str] = Field(default=None)
    link: Optional[str] = Field(default=None)
    vencimento_base: float = Field(default=None)
    vencimento_hora: float = Field(default=None)
    valor_liquido: float = Field(default=None)
    valor_bruto: float = Field(default=None)
    descontos_totais: float = Field(default=None)
    dias_trabalhados: int = Field(default=None)
    data_fecho: date = Field(default=None)
    ano: int = Field(default=None)
    mes: int = Field(default=None)
    entidade_id: Optional[int] = Field(default=None)
    ativo: bool = Field(default=None)
    data_criacao: datetime = Field(default=None)
    data_alteracao: datetime = Field(default=None)


