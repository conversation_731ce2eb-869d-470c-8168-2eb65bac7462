from comum.services.logger import setup_logger
from comum.models.eventos import EventoBase, EventoUpdate
from comum.models.comprovativos import ComprovativoUpdate
from app.services.api_service import ApiService


logger = setup_logger()

class ComprovativosHandler:
    def __init__(self, api_service: ApiService):
        self.api_service = api_service
        

    def processar_evento(self, evento: EventoBase):
        try:
            comprovativo = self.api_service.procura_comprovativo(evento.fkid)
            conta = self.api_service.procura_conta(comprovativo.conta_id)
            padroes = self.api_service.get_comprova_padroes()
            new_comprovativo = None
            

            if evento.tipo == 'INSERT':
                for padrao in padroes:
                    if padrao.padrao.lower() in comprovativo.movimento.lower():
                        new_comprovativo = self.api_service.update_comprovativo(comprovativo_id=comprovativo.id, comprovativo=ComprovativoUpdate(segmento_id=padrao.segmento_id, entidade_id=padrao.entidade_id))
                        break
                if new_comprovativo:
                    self.api_service.update_evento(evento_id=evento.id, evento=EventoUpdate(estado=200, obs="Comprovativo atualizado"))
        
                else:
                    self.api_service.update_evento(evento_id=evento.id, evento=EventoUpdate(estado=100, obs="Comprovativo sem padrao configurado"))



            # elif evento.tipo == 'UPDATE':
            #     if conta.utilizador == 'ricardo':
            #         if comprovativo.segmento_id == 4: # se for transferencia calcula o cont_trf
            #             movimento_ultimos_9 = comprovativo.movimento[-9:]
            #             comprovativo.cont_trf = movimento_ultimos_9[:3] + movimento_ultimos_9[-3:]
            #             response = self.api_service.update_comprovativo(comprovativo_id=comprovativo.id, comprovativo=ComprovativoUpdate(cont_trf=comprovativo.cont_trf))
            #             if response.status_code == 200:
            #                 self.api_service.update_evento(evento_id=evento.id, evento=EventoUpdate(estado=200, obs=None))
            #             else:
            #                 self.api_service.update_evento(evento_id=evento.id, evento=EventoUpdate(estado=99, obs='update nao processado'))
            #         else:
            #             self.api_service.update_evento(evento_id=evento.id, evento=EventoUpdate(estado=100, obs='segmento sem condicoes'))
                
            #     elif conta.utilizador == 'neide':
            #         if comprovativo.segmento_id == 4:
            #             comprovativo.cont_trf = comprovativo.movimento[-4:]
            #             response = self.api_service.update_comprovativo(comprovativo_id=comprovativo.id, comprovativo=ComprovativoUpdate(cont_trf=comprovativo.cont_trf))
            #             if response.status_code == 200:
            #                 self.api_service.update_evento(evento_id=evento.id, evento=EventoUpdate(estado=200, obs=None))
            #             else:
            #                 self.api_service.update_evento(evento_id=evento.id, evento=EventoUpdate(estado=99, obs='update nao processado'))
            #         else:
            #             self.api_service.update_evento(evento_id=evento.id, evento=EventoUpdate(estado=100, obs='segmento sem condicoes'))
            #     else:
            #         self.api_service.update_evento(evento_id=evento.id, evento=EventoUpdate(estado=100, obs='utilizador sem condicoes'))

            else:
                self.api_service.update_evento(evento_id=evento.id, evento=EventoUpdate(estado=100, obs=f'tipo de evento: {evento.tipo.upper()} não configurado'))

        except Exception as e:
            # self.api_service.update_evento(evento_id=evento.id, evento=EventoUpdate(estado=99, obs=f"Erro processar_evento(): {str(e)}"))
            logger.error(msg=f"Erro processar_evento(): {str(e)}",ThreadName="ComprovativosHandler",Application="pgsqlops", exc_info=True) # type: ignore
