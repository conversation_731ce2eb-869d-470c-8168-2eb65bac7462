import re
from datetime import datetime
from typing import Optional
from src.services.log import get_funcao
from src.services.file_service import FileService
from src.faturas.classFatura import Fatura, Cabecalho, Linhas


tabela = 'lidl'

class FaturaLidlExtractor:
    def __init__(self, pdf):
        self.file = FileService(file=pdf)
        self.texto = self.file.extrair_texto_pdfplumber()
    
    
    def extrair_data(self) -> datetime:
        funcao = get_funcao()
        padrao = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})'
        match = re.search(padrao, self.texto)
        
        if match:
            value = match.group(1)
            value = value.replace('-', '/')
            value = datetime.strptime(value, '%Y/%m/%d %H:%M:%S')
        else:
            value = datetime(1900, 1, 1, 0, 0, 0)
            
            
        message = {
            'valor': value,
            'match': match
        }
        return value
    
    
    def extrair_nif(self) -> Optional[str]:
        funcao = get_funcao()
        padrao = r'(Contribuinte....: |NIF...: )(\d+)'
        match = re.search(padrao, self.texto) 
        if match:
            value = match.group(2)
        else:
            value = None
            
            
        message = {
            'valor': value,
            'match': match
        }
        return value
    
    def extrair_nif_entidade(self) -> Optional[str]:
        funcao = get_funcao()
        padrao = r'(?<=NIF:)\d+'
        match = re.search(padrao, self.texto) 
        if match:
            value = match.group(0)
        else:
            value = None
            
            
        message = {
            'valor': value,
            'match': match
        }
        return value
    
    def extrair_fatura(self) -> Optional[str]:
        funcao = get_funcao()
        padrao = r'No : (\w{2} \d+/\d+)'
        match = re.search(padrao, self.texto) 
        if match:
            value = match.group(1)
        else:
            value = None
            
            
        message = {
            'valor': value,
            'match': match
        }
        return value

    def extrair_valor(self) -> Optional[float]:
        funcao = get_funcao()
        padrao = r'Total (\d+,\d{2})'
        match = re.search(padrao, self.texto) 
        if match:
            value = match.group(1)
            value = value.replace('.', '').replace(',', '.')
            value = float(value)
        else:
            value = 0
            
            
        message = {
            'valor': value,
            'match': match
        }
        return value
    
    def extrair_desconto(self) -> Optional[float]:
        funcao = get_funcao()
        padrao = r'Total em descontos (\d+,\d{2})'
        match = re.search(padrao, self.texto) 
        if match:
            value = match.group(1)
            value = value.replace('.', '').replace(',', '.')
            value = float(value)
        else:
            value = 0
            
            
        message = {
            'valor': value,
            'match': match
        }
        return value
    
    def extrair_base_23(self) -> Optional[float]:
        funcao = get_funcao()
        padrao = r"([A])\s+(\d+%)+\s+(\d+\,\d+)+\s+(\d+\,\d+)+\s+(\d+\,\d+)"
        match = re.search(padrao, self.texto) 
        if match:
            value = match.group(3)
            value = value.replace('.', '').replace(',', '.')
            value = float(value)
        else:
            value = 0
            
            
        message = {
            'valor': value,
            'match': match
        }
        return value

    def extrair_iva_23(self) -> Optional[float]:
        funcao = get_funcao()
        padrao = r"([A])\s+(\d+%)+\s+(\d+\,\d+)+\s+(\d+\,\d+)+\s+(\d+\,\d+)"
        match = re.search(padrao, self.texto) 
        if match:
            value = match.group(5)
            value = value.replace('.', '').replace(',', '.')
            value = float(value)
        else:
            value = 0
            
            
        message = {
            'valor': value,
            'match': match
        }
        return value
    
    def extrair_base_06(self) -> Optional[float]:
        funcao = get_funcao()
        padrao = r"([B])\s+(\d+%)+\s+(\d+\,\d+)+\s+(\d+\,\d+)+\s+(\d+\,\d+)"
        match = re.search(padrao, self.texto) 
        if match:
            value = match.group(3)
            value = value.replace('.', '').replace(',', '.')
            value = float(value)
        else:
            value = 0
            
            
        message = {
            'valor': value,
            'match': match
        }
        return value
    
    def extrair_iva_06(self) -> Optional[float]:
        funcao = get_funcao()
        padrao = r"([B])\s+(\d+%)+\s+(\d+\,\d+)+\s+(\d+\,\d+)+\s+(\d+\,\d+)"
        match = re.search(padrao, self.texto) 
        if match:
            value = match.group(5)
            value = value.replace('.', '').replace(',', '.')
            value = float(value)
        else:
            value = 0
            
            
        message = {
            'valor': value,
            'match': match
        }
        return value
    
    def extrair_entidade(self) -> Optional[int]:
        funcao = get_funcao()
        value = 1
        message = {
            'valor': value,
            'match': 'sem padrao configurado'
        }        
        return value
    
    def extrair_linhas(self):
        funcao = get_funcao()
        linhas_texto = self.texto.split('\n')
        padrao_linha = r'^(.*?)\s+(\d+,\d{2})\s+([AB])$'
        padrao_linha_desconto = r'^(.*) -(\d+,\d{2})$'
        linhas_extraidas = []
        for linha in linhas_texto:
            match_linha = re.match(padrao_linha, linha)
            match_desconto = re.match(padrao_linha_desconto, linha)
            if match_linha:
                artigo = match_linha.group(1).strip()
                tx_iva = float(match_linha.group(3).replace('A', '0.23').replace('B', '0.06'))
                valor_bruto_civa = float(match_linha.group(2).replace(',', '.'))
                linhas_extraidas.append(Linhas(artigo=artigo, tx_iva=tx_iva, valor=valor_bruto_civa))

            
            desconto_civa = 0.00
            if match_desconto:
                desconto_civa = float(match_desconto.group(2).replace(',', '.'))
                linhas_extraidas[-1].valor = round(linhas_extraidas[-1].valor - desconto_civa, 2)
                linhas_extraidas[-1].desconto = desconto_civa
            
        if linhas_extraidas:
            for linha in linhas_extraidas:
                linha.iva = round(linha.valor * (linha.tx_iva / (1 + linha.tx_iva)), 2) 
                linha.base = round(linha.valor - linha.iva, 2)
                if linha.desconto:
                    linha.desconto = round(linha.desconto / (1 + linha.tx_iva), 2 )
                    linha.valor_bruto = round(linha.base + linha.desconto, 2)
                else:
                    linha.desconto = 0
                    linha.valor_bruto = round(linha.base, 2)
        return linhas_extraidas






    def run_extraction(self) -> Fatura:
        funcao = get_funcao()
        data = self.extrair_data()
        nif = self.extrair_nif()
        nif_entidade = self.extrair_nif_entidade()
        fatura = self.extrair_fatura()
        valor = self.extrair_valor()
        desconto = self.extrair_desconto()
        base_23 = self.extrair_base_23()
        iva_23 = self.extrair_iva_23()
        base_06 = self.extrair_base_06()
        iva_06 = self.extrair_iva_06()
        entidade_id = self.extrair_entidade()
        ano = data.year if data else None
        mes = data.month if data else None
        base = base_23 + base_06 if base_23 and base_06 else None
        iva = round(iva_23 + iva_06, 2) if iva_23 and iva_06 else None

        cabecalho = Cabecalho(
                                data=data,
                                nif=nif,
                                nif_entidade=nif_entidade,
                                fatura=fatura,
                                valor=valor,
                                desconto=desconto,
                                entidade_id=entidade_id,
                                ano=ano,
                                mes=mes,
                                base=base,
                                iva=iva
                            )



        linhas_extraidas = self.extrair_linhas()

        for linha in linhas_extraidas:
            linha.data = cabecalho.data
            linha.fatura = cabecalho.fatura
            linha.base = round(linha.valor / (1 + linha.tx_iva), 2)
            linha.iva = linha.base * linha.tx_iva
            linha.ano = linha.data.year if linha.data else None
            linha.mes = linha.data.month if linha.data else None
        fatura_lidl: Fatura = Fatura(cabecalho=cabecalho, linhas=linhas_extraidas)
        fatura_lidl.cabecalho.valor_bruto = sum([linha.valor_bruto for linha in linhas_extraidas])
        # message = {
        #     'recibo': fatura_lidl.model_dump(),
        # }
        # LogApp.log_info(classe=tabela, evento=funcao, resultado=message)
        return fatura_lidl
    


