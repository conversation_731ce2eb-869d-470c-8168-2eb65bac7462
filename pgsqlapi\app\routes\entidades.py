from typing import List
from sqlmodel import Session
from fastapi import APIRouter, Depends
from comum.models.entidades import EntidadeBase, EntidadeCreate, EntidadeUpdate
from app.database.postgres import get_session
from app.workers.entidades import EntidadesWorker


entidades_router = APIRouter()

def get_worker(session: Session = Depends(get_session)) -> EntidadesWorker:
    return EntidadesWorker(session)


@entidades_router.get("/entidades", response_model=List[EntidadeBase])
async def lista_completa(worker: EntidadesWorker = Depends(get_worker)):
    return list(worker.lista())


@entidades_router.get("/entidades/{id}", response_model=EntidadeBase)
async def procura(id: int, worker: EntidadesWorker = Depends(get_worker)):
    return worker.procura(id)


@entidades_router.post("/entidades", response_model=EntidadeBase)
async def cria(novos_dados: EntidadeCreate, worker: EntidadesWorker = Depends(get_worker)):
    return worker.cria(novos_dados)


@entidades_router.patch("/entidades/{id}", response_model=EntidadeBase)
async def atualiza(id: int, dados: EntidadeUpdate, worker: EntidadesWorker = Depends(get_worker)):
    return worker.atualiza(id, dados)
    

@entidades_router.delete("/entidades/{id}")
async def remove(id: int, worker: EntidadesWorker = Depends(get_worker)):
    worker.remove(id)
    return {"message": f"ID: **{id}** deleted!"}