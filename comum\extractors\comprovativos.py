import re
from typing import Optional
from datetime import datetime
from comum.models.comprovativos import ComprovativoCreate
from comum.services.logger import setup_logger
logger = setup_logger()

class ComprovativoExtractor:
    def __init__(self, texto, nome_ficheiro):
        self.texto = texto
        self.nome_ficheiro = nome_ficheiro
        self.comprovativo = ComprovativoCreate()

    def extrair_data_valor(self) -> datetime:
        padrao = r"Data valor (\d{2}-\d{2}-\d{4})"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            value = value.replace('-', '/')
            value = datetime.strptime(value, '%d/%m/%Y')
            return value
        else:
            value = datetime(1900, 1, 1, 0, 0, 0)
            return value

    def extrair_data_movimento(self) -> datetime:
        padrao = r"Data do movimento (\d{2}-\d{2}-\d{4}( \d{2}:\d{2})?)"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            value = value.replace('-', '/')
            if len(value) > 10:
                value = datetime.strptime(value, '%d/%m/%Y %H:%M')
            else:
                value = datetime.strptime(value, '%d/%m/%Y')
            return value
        else:
            value = datetime(1900, 1, 1, 0, 0, 0)
            return value
    
    def extrair_valor(self) -> float:
        padrao = r"Montante (\d+(?:\.\d{3})*(?:,\d{2})?)"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            value = value.replace(' ', '').replace('.', '').replace(',', '.')
            value = float(value)
            return value
        else:
            value = 0.00
            return value
    
    def extrair_conta(self) -> Optional[str] | None:
        padrao = r"Conta (\d+) - ([A-Z]+) - Conta Extracto"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            return value
        else:
            value = None
            return value
    
    def extrair_movimento(self) -> Optional[str]:
        padrao = r"Descrição (.+)"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            return value
        else:
            value = 'sem movimento'
            return value
    
    def extrair_tipo_movimento(self) -> str:
        padrao= r"Tipo de movimento (.+)"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1) 
            value = value[:1].upper()
            return value
        else:
            value = 'X'
            return value
            
    def extrair_entidade(self) -> Optional[str]:
        padrao = r"Entidade (.+)"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1).strip()
            value = re.sub(r'\s+', ' ', value).strip()
            return value
        else:
            value = None
            return value
    
    def extrair_numero_cartao(self) -> Optional[str]:
        padrao = r"Número do cartão (\d{4} \*\*\*\* \*\*\*\* \d{4})"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            return value
        else:
            value = None
            return value

    def extrair_tipo_pagamento(self) -> Optional[str]: 
        padrao = r"Meio de pagamento utilizado (.+)"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            return value
        else:
            value = None
            return value
    
    def extrair_id_sibs(self) -> Optional[str]:
        padrao = r"N.º  Identificação SIBS (\d+)"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            return value
        else:
            value = None
            return value

    def extrair_ordenante(self) -> Optional[str]:
        padrao = r"Nome Ordenante (.+)"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            return value
        else:
            value = None
            return value
    
    def extrair_conta_destino(self) -> Optional[str]:
        padrao = r"Conta destino (.+)"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            return value
        else:
            value = None
            return value

    def extrair_origem_operacao(self) -> Optional[str]:
        padrao = r"Operação efectuada em (.+)"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            return value
        else:
            value = None
            return value

    def extrair_id_transferencia(self) -> Optional[str]:
        padrao = r"Nº de Transferência (.+)"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            return value
        else:
            value = None
            return value
    
    def extrair_local(self) -> Optional[str]:
        padrao = r"Local(.+)"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            return value
        else:
            value = None
            return value

    def extrair_swift_destinatario(self) -> Optional[str]:
        padrao = r"Swift do destinatário (.+)"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            return value
        else:
            value = None
            return value

    def extrair_iban_destinatario(self) -> Optional[str]:
        padrao = r"Iban do destinatário (.+)"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            return value
        else:
            value = None
            return value

    def extrair_motivo_sepa(self) -> Optional[str]:
        padrao = r"Motivo SEPA (.+)"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            return value
        else:
            value = None
            return value
    
    def extrair_observacao(self) -> Optional[str]:
        padrao = r"Descritivo conta destino (.+)"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            return value
        else:
            value = None
            return value


    def run_extraction(self) -> ComprovativoCreate:
            try:
                self.comprovativo.data_valor = self.extrair_data_valor()
                self.comprovativo.data_movimento = self.extrair_data_movimento()
                self.comprovativo.valor = self.extrair_valor()
                # conta = self.extrair_conta()
                self.comprovativo.movimento = self.extrair_movimento()
                self.comprovativo.tipo_movimento = self.extrair_tipo_movimento()
                self.comprovativo.entidade = self.extrair_entidade()
                self.comprovativo.numero_cartao = self.extrair_numero_cartao()
                self.comprovativo.tipo_pagamento = self.extrair_tipo_pagamento()
                self.comprovativo.id_sibs = self.extrair_id_sibs()
                self.comprovativo.ordenante = self.extrair_ordenante()
                self.comprovativo.conta_destino = self.extrair_conta_destino()
                self.comprovativo.origem_operacao = self.extrair_origem_operacao()
                self.comprovativo.id_transferencia = self.extrair_id_transferencia()
                self.comprovativo.local = self.extrair_local()
                self.comprovativo.swift_destinatario = self.extrair_swift_destinatario()
                self.comprovativo.iban_destinatario = self.extrair_iban_destinatario()
                self.comprovativo.motivo_sepa = self.extrair_motivo_sepa()
                self.comprovativo.observacao = self.extrair_observacao()
                self.comprovativo.ano = self.comprovativo.data_movimento.year if self.comprovativo.data_movimento else None
                self.comprovativo.mes = self.comprovativo.data_movimento.month if self.comprovativo.data_movimento else None
                self.comprovativo.conta_id = 0
                self.comprovativo.entidade_id = 0
                self.comprovativo.segmento_id = 0
                logger.info(msg=f"COMPROVATIVO EXTRACTION: {self.nome_ficheiro}", ThreadName="ComprovativoExtractor", Application="pgsqlapi", dados=self.comprovativo) # type: ignore
                return self.comprovativo
            except Exception as e:
                mensagem = str(e).strip().split("\n")[0]
                logger.error(msg=f"{type(e).__name__} - {mensagem}", ThreadName="ComprovativoExtractor", Application="pgsqlapi", exc_info=e) # type: ignore
                raise ValueError(f"ERRO AO EXTRAIR COMPROVATIVO: {mensagem}")



