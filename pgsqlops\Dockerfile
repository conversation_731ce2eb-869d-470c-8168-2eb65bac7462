FROM python:3.13-slim

WORKDIR /app

# Copia apenas o requirements.txt primeiro para aproveitar o cache do Docker
COPY requirements_ops.txt .

# Instala as dependências do sistema necessárias para o psycopg2
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    gcc \
    python3-dev \
    libpq-dev && \
    rm -rf /var/lib/apt/lists/*

# Instala as dependências Python
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements_ops.txt

# Copia o resto dos arquivos
COPY . .

# Expõe a porta da API
EXPOSE 5002
