import re
from typing import Optional
import PyPDF2
from io import Bytes<PERSON>
from datetime import datetime
from src.services.log import get_funcao
from src.comprovativos.classComprovativo import Comprovativo
from src.logs.classLogsApp import LogApp
from src.services.logger import setup_logger
logger = setup_logger()

tabela = 'comprovativos'

class ComprovativoExtractor:
    def __init__(self, pdf):
        self.texto, self.file = self.ler_pdf(pdf)

    def ler_pdf(self, pdf):
        funcao = get_funcao()
        try:         
            if isinstance(pdf, BytesIO):
                origem = 'streamlit'
                reader = PyPDF2.PdfReader(pdf)
                texto = ''.join(page.extract_text() for page in reader.pages)
                file = pdf
                message = {
                    'origem': origem,
                    'texto': texto,
                }

            elif isinstance(pdf, str):
                origem = 'local'
                with open(pdf, "rb") as pdf_file:
                    reader = PyPDF2.PdfReader(pdf_file)
                    texto = ''.join(page.extract_text() for page in reader.pages)    
                    file = pdf_file
                message = {
                    'origem': origem,
                    'texto': texto,
                }
            else:
                message = {
                    'erro': 'Tipo de arquivo não suportado!',
                }
                logger.error(msg="", Table=tabela.lower(), ThreadName="ComprovativoExtractor", **message)                
                raise ValueError("Tipo de arquivo não suportado!")
            

            if not texto:
                message = {
                    'erro': 'PDF VAZIO!',
                }
                logger.error(msg="", Table=tabela.lower(), ThreadName="ComprovativoExtractor", **message)                
                raise ValueError("PDF VAZIO!")
                 
            return texto, file
        
        except Exception as e:
            message = {
                'erro': e,
            }
            logger.error(msg="", Table=tabela.lower(), ThreadName="ComprovativoExtractor", **message)            
            raise ValueError(f"ERRO AO LER PDF: {e}")

    def extrair_data_valor(self) -> datetime:
        padrao = r"Data valor (\d{2}-\d{2}-\d{4})"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            value = value.replace('-', '/')
            value = datetime.strptime(value, '%d/%m/%Y')
            message = {
                'valor': value,
                'match': match
            }
            return value
        else:
            value = datetime(1900, 1, 1, 0, 0, 0)
            message = {
                'valor': value,
                'match': 'padrao nao encontrado'
            }
            return value

    def extrair_data_movimento(self) -> datetime:
        padrao = r"Data do movimento (\d{2}-\d{2}-\d{4}( \d{2}:\d{2})?)"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            value = value.replace('-', '/')
            if len(value) > 10:
                value = datetime.strptime(value, '%d/%m/%Y %H:%M')
            else:
                value = datetime.strptime(value, '%d/%m/%Y')
            message = {
                'valor': value,
                'match': match
            }
            return value
        else:
            value = datetime(1900, 1, 1, 0, 0, 0)
            message = {
                'valor': value,
                'match': 'padrao nao encontrado'
            }
            return value
    
    def extrair_valor(self) -> float:
        padrao = r"Montante (\d+(?:\.\d{3})*(?:,\d{2})?)"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            value = value.replace(' ', '').replace('.', '').replace(',', '.')
            value = float(value)
            message = {
                'valor': value,
                'match': match
            }
            return value
        else:
            value = 0.00
            message = {
                'valor': value,
                'match': 'padrao nao encontrado'
            }
            return value
    
    def extrair_conta(self) -> Optional[str] | None:
        funcao = get_funcao()
        padrao = r"Conta (\d+) - ([A-Z]+) - Conta Extracto"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            message = {
                'valor': value,
                'match': match
            }
            return value
        else:
            value = None
            message = {
                'valor': value,
                'match': 'padrao nao encontrado'
            }
            return value
    
    def extrair_movimento(self) -> Optional[str]:
        funcao = get_funcao()
        padrao = r"Descrição (.+)"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            message = {
                'valor': value,
                'match': match
            }
            return value
        else:
            value = 'sem movimento'
            message = {
                'valor': value,
                'match': 'padrao nao encontrado'
            }
            return value
    
    def extrair_tipo_movimento(self) -> str:
        funcao = get_funcao()
        padrao= r"Tipo de movimento (.+)"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1) 
            value = value[:1].upper()
            message = {
                'valor': value,
                'match': match
            }
            return value
        else:
            value = 'X'
            message = {
                'valor': value,
                'match': 'padrao nao encontrado'
            }
            return value
            
    def extrair_entidade(self) -> Optional[str]:
        funcao = get_funcao()
        padrao = r"Entidade (.+)"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1).strip()
            value = re.sub(r'\s+', ' ', value).strip()
            message = {
                'valor': value,
                'match': match
            }
            return value
        else:
            value = None
            message = {
                'valor': value,
                'match': 'padrao nao encontrado'
            }
            return value
    
    def extrair_numero_cartao(self) -> Optional[str]:
        funcao = get_funcao()
        padrao = r"Número do cartão (\d{4} \*\*\*\* \*\*\*\* \d{4})"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            message = {
                'valor': value,
                'match': match
            }
            return value
        else:
            value = None
            message = {
                'valor': value,
                'match': 'padrao nao encontrado'
            }
            return value

    def extrair_tipo_pagamento(self) -> Optional[str]: 
        funcao = get_funcao()
        padrao = r"Meio de pagamento utilizado (.+)"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            message = {
                'valor': value,
                'match': match
            }
            return value
        else:
            value = None
            message = {
                'valor': value,
                'match': 'padrao nao encontrado'
            }
            return value
    
    def extrair_id_sibs(self) -> Optional[str]:
        funcao = get_funcao()
        padrao = r"N.º  Identificação SIBS (\d+)"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            message = {
                'valor': value,
                'match': match
            }
            return value
        else:
            value = None
            message = {
                'valor': value,
                'match': 'padrao nao encontrado'
            }
            return value

    def extrair_ordenante(self) -> Optional[str]:
        funcao = get_funcao()
        padrao = r"Nome Ordenante (.+)"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            message = {
                'valor': value,
                'match': match
            }
            return value
        else:
            value = None
            message = {
                'valor': value,
                'match': 'padrao nao encontrado'
            }
            return value
    
    def extrair_conta_destino(self) -> Optional[str]:
        funcao = get_funcao()
        padrao = r"Conta destino (.+)"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            message = {
                'valor': value,
                'match': match
            }
            return value
        else:
            value = None
            message = {
                'valor': value,
                'match': 'padrao nao encontrado'
            }
            return value

    def extrair_origem_operacao(self) -> Optional[str]:
        funcao = get_funcao()
        padrao = r"Operação efectuada em (.+)"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            message = {
                'valor': value,
                'match': match
            }
            return value
        else:
            value = None
            message = {
                'valor': value,
                'match': 'padrao nao encontrado'
            }
            return value

    def extrair_id_transferencia(self) -> Optional[str]:
        funcao = get_funcao()
        padrao = r"Nº de Transferência (.+)"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            message = {
                'valor': value,
                'match': match
            }
            return value
        else:
            value = None
            message = {
                'valor': value,
                'match': 'padrao nao encontrado'
            }
            return value
    
    def extrair_local(self) -> Optional[str]:
        funcao = get_funcao()
        padrao = r"Local(.+)"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            message = {
                'valor': value,
                'match': match
            }
            return value
        else:
            value = None
            message = {
                'valor': value,
                'match': 'padrao nao encontrado'
            }
            return value

    def extrair_swift_destinatario(self) -> Optional[str]:
        funcao = get_funcao()
        padrao = r"Swift do destinatário (.+)"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            message = {
                'valor': value,
                'match': match
            }
            return value
        else:
            value = None
            message = {
                'valor': value,
                'match': 'padrao nao encontrado'
            }
            return value

    def extrair_iban_destinatario(self) -> Optional[str]:
        funcao = get_funcao()
        padrao = r"Iban do destinatário (.+)"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            message = {
                'valor': value,
                'match': match
            }
            return value
        else:
            value = None
            message = {
                'valor': value,
                'match': 'padrao nao encontrado'
            }
            return value

    def extrair_motivo_sepa(self) -> Optional[str]:
        funcao = get_funcao()
        padrao = r"Motivo SEPA (.+)"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            message = {
                'valor': value,
                'match': match
            }
            return value
        else:
            value = None
            message = {
                'valor': value,
                'match': 'padrao nao encontrado'
            }
            return value
    
    def extrair_observacao(self) -> Optional[str]:
        funcao = get_funcao()
        padrao = r"Descritivo conta destino (.+)"
        match = re.search(padrao, self.texto)
        if match:
            value = match.group(1)
            message = {
                'valor': value,
                'match': match
            }
            return value
        else:
            value = None
            message = {
                'valor': value,
                'match': 'padrao nao encontrado'
            }
            return value


    def run_extraction(self):
        funcao = get_funcao()
        data_valor = self.extrair_data_valor()
        data_movimento = self.extrair_data_movimento()
        valor = self.extrair_valor()
        conta = self.extrair_conta()
        movimento = self.extrair_movimento()
        tipo_movimento = self.extrair_tipo_movimento()
        entidade = self.extrair_entidade()
        numero_cartao = self.extrair_numero_cartao()
        tipo_pagamento = self.extrair_tipo_pagamento()
        id_sibs = self.extrair_id_sibs()
        ordenante = self.extrair_ordenante()
        conta_destino = self.extrair_conta_destino()
        origem_operacao = self.extrair_origem_operacao()
        id_transferencia = self.extrair_id_transferencia()
        local = self.extrair_local()
        swift_destinatario = self.extrair_swift_destinatario()
        iban_destinatario = self.extrair_iban_destinatario()
        motivo_sepa = self.extrair_motivo_sepa()
        observacao = self.extrair_observacao()
        ano = data_movimento.year if data_movimento else None
        mes = data_movimento.month if data_movimento else None
        comprovativo = Comprovativo(
                                    data_valor=data_valor,
                                    data_movimento=data_movimento,
                                    valor=valor,
                                    movimento=movimento,
                                    tipo_movimento=tipo_movimento,
                                    entidade=entidade,
                                    numero_cartao=numero_cartao,
                                    tipo_pagamento=tipo_pagamento,
                                    id_sibs=id_sibs,
                                    ordenante=ordenante,
                                    conta_destino=conta_destino,
                                    origem_operacao=origem_operacao,
                                    id_transferencia=id_transferencia,
                                    local=local,
                                    swift_destinatario=swift_destinatario,
                                    iban_destinatario=iban_destinatario,
                                    motivo_sepa=motivo_sepa,
                                    observacao=observacao,
                                    ano=ano,
                                    mes=mes,
                                    link=None,
                                    conta_id=2,
                                    segmento_id=0,
                                    entidade_id=0,
                                    ativo=True,
                                    data_criacao=None,
                                    data_alteracao=None
                                ).dict(exclude_unset=True, exclude_none=True)
        logger.info(msg="", Table=tabela.lower(), ThreadName="ComprovativoExtractor", **comprovativo)
        return comprovativo