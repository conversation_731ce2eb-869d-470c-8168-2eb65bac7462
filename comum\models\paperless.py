from sqlmodel import SQLModel, Field
from typing import Optional, List, Any
from pydantic import BaseModel


class CustomField(BaseModel):
    value: Any
    field: int




class PaperlessDocument(SQLModel):
    id: int
    correspondent: Optional[Any] = None
    document_type: Optional[Any] = None
    storage_path: Optional[Any] = None
    title: Optional[str] = None
    content: Optional[str] = None
    tags: List[Any] = []
    created: Optional[str] = None
    created_date: Optional[str] = None
    modified: Optional[str] = None
    added: Optional[str] = None
    deleted_at: Optional[Any] = None
    archive_serial_number: Optional[Any] = None
    original_file_name: Optional[str] = None
    archived_file_name: Optional[str] = None
    owner: Optional[int] = None
    user_can_change: Optional[bool] = None
    is_shared_by_requester: Optional[bool] = None
    notes: Optional[List[Any]] = None
    custom_fields: Optional[List[CustomField]] = None
    page_count: Optional[int] = None
    mime_type: Optional[str] = None




class PaperlessModel(SQLModel):
    count: Optional[int] = None
    next: Optional[Any] = None
    previous: Optional[Any] = None
    all: List[int] = []
    results: List[PaperlessDocument]


class PaperlessTag(SQLModel):
    id: int
    slug: str
    name: str
    color: str
    text_color: str
    match: str
    matching_algorithm: int
    is_insensitive: bool
    is_inbox_tag: bool
    document_count: int
    owner: int
    user_can_change: bool


class PaperlessTagModel(SQLModel):
    count: Optional[int] = None
    next: Optional[Any] = None
    previous: Optional[Any] = None
    all: List[int] = []
    results: List[PaperlessTag]