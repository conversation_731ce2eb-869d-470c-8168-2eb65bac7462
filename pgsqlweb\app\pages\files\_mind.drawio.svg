<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" style="background: transparent; background-color: transparent;" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="731px" height="571px" viewBox="-0.5 -0.5 731 571" content="&lt;mxfile&gt;&lt;diagram id=&quot;hzL6zOtJ6pmT5DsXj4Jl&quot; name=&quot;Page-1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <g>
            <path d="M 360 160 L 360 130 L 520 130 L 520 160" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 360 160 L 360 290 L 520 290 L 520 160" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 360 160 L 520 160" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 390 160 L 390 190 L 390 220 L 390 250 L 390 280" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 145px; margin-left: 440px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: nowrap; ">
                                    eventos
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="440" y="149" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle" font-weight="bold">
                        eventos
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 360 160 M 520 160 M 520 190 L 360 190" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="360" y="160" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 360 160 M 390 160 M 390 190 M 360 190" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 28px; height: 1px; padding-top: 175px; margin-left: 361px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; max-height: 26px; overflow: hidden; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; ">
                                    PK
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="375" y="179" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle" font-weight="bold">
                        PK
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="390" y="160" width="130" height="30" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 390 160 M 520 160 M 520 190 M 390 190" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 122px; height: 1px; padding-top: 175px; margin-left: 398px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; max-height: 26px; overflow: hidden; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; text-decoration: underline; white-space: normal; word-wrap: normal; ">
                                    id_chave
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="398" y="179" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" font-weight="bold" text-decoration="underline">
                        id_chave
                    </text>
                </switch>
            </g>
        </g>
        <g/>
        <g>
            <rect x="360" y="190" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 360 190 M 390 190 M 390 220 M 360 220" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 28px; height: 1px; padding-top: 205px; margin-left: 361px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; max-height: 26px; overflow: hidden; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    FK
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="375" y="209" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        FK
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="390" y="190" width="130" height="30" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 390 190 M 520 190 M 520 220 M 390 220" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 122px; height: 1px; padding-top: 205px; margin-left: 398px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; max-height: 26px; overflow: hidden; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    <div>
                                        <span style="background-color: transparent;">
                                            <b>
                                                fkid_chave
                                            </b>
                                        </span>
                                        <br/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="398" y="209" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        fkid_chave
                    </text>
                </switch>
            </g>
        </g>
        <g/>
        <g>
            <rect x="360" y="220" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 360 220 M 390 220 M 390 250 M 360 250" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="390" y="220" width="130" height="30" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 390 220 M 520 220 M 520 250 M 390 250" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 122px; height: 1px; padding-top: 235px; margin-left: 398px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; max-height: 26px; overflow: hidden; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    estado (0/99/100)
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="398" y="239" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        estado (0/99/100)
                    </text>
                </switch>
            </g>
        </g>
        <g/>
        <g>
            <rect x="360" y="250" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 360 250 M 390 250 M 390 280 M 360 280" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="390" y="250" width="130" height="30" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 390 250 M 520 250 M 520 280 M 390 280" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 0 40 L 0 10 L 160 10 L 160 40" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 0 40 L 0 170 L 160 170 L 160 40" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 0 40 L 160 40" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 30 40 L 30 70 L 30 100 L 30 130 L 30 160" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 25px; margin-left: 80px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: nowrap; ">
                                    teste
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="80" y="29" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle" font-weight="bold">
                        teste
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 0 40 M 160 40 M 160 70 L 0 70" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="0" y="40" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 0 40 M 30 40 M 30 70 M 0 70" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 28px; height: 1px; padding-top: 55px; margin-left: 1px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; max-height: 26px; overflow: hidden; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; ">
                                    PK
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="15" y="59" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle" font-weight="bold">
                        PK
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="30" y="40" width="130" height="30" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 30 40 M 160 40 M 160 70 M 30 70" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 122px; height: 1px; padding-top: 55px; margin-left: 38px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; max-height: 26px; overflow: hidden; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; text-decoration: underline; white-space: normal; word-wrap: normal; ">
                                    id_chave
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="38" y="59" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" font-weight="bold" text-decoration="underline">
                        id_chave
                    </text>
                </switch>
            </g>
        </g>
        <g/>
        <g>
            <rect x="0" y="70" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 0 70 M 30 70 M 30 100 M 0 100" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="30" y="70" width="130" height="30" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 30 70 M 160 70 M 160 100 M 30 100" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g/>
        <g>
            <rect x="0" y="100" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 0 100 M 30 100 M 30 130 M 0 130" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="30" y="100" width="130" height="30" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 30 100 M 160 100 M 160 130 M 30 130" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g/>
        <g>
            <rect x="0" y="130" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 0 130 M 30 130 M 30 160 M 0 160" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="30" y="130" width="130" height="30" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 30 130 M 160 130 M 160 160 M 30 160" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="560" y="0" width="170" height="70" rx="35" ry="35" fill="#1ca5b8" stroke="none" pointer-events="all" style="fill: light-dark(rgb(28, 165, 184), rgb(25, 143, 159));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 152px; height: 1px; padding-top: 35px; margin-left: 569px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #ffffff; ">
                                <div style="display: inline-block; font-size: 14px; font-family: &quot;Helvetica&quot;; color: light-dark(#ffffff, #121212); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; ">
                                    WorkerEventos
                                    <br/>
                                    <font style="color: light-dark(rgb(0, 0, 0), rgb(51, 51, 51));">
                                        cron 1min
                                    </font>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="645" y="39" fill="#ffffff" font-family="&quot;Helvetica&quot;" font-size="14px" text-anchor="middle" font-weight="bold">
                        WorkerEventos...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 0 240 L 0 210 L 160 210 L 160 240" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 0 240 L 0 370 L 160 370 L 160 240" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 0 240 L 160 240" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 30 240 L 30 270 L 30 300 L 30 330 L 30 360" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 225px; margin-left: 80px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: nowrap; ">
                                    comprovativos
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="80" y="229" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle" font-weight="bold">
                        comprovativos
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 0 240 M 160 240 M 160 270 L 0 270" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="0" y="240" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 0 240 M 30 240 M 30 270 M 0 270" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 28px; height: 1px; padding-top: 255px; margin-left: 1px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; max-height: 26px; overflow: hidden; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; ">
                                    PK
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="15" y="259" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle" font-weight="bold">
                        PK
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="30" y="240" width="130" height="30" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 30 240 M 160 240 M 160 270 M 30 270" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 122px; height: 1px; padding-top: 255px; margin-left: 38px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; max-height: 26px; overflow: hidden; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; text-decoration: underline; white-space: normal; word-wrap: normal; ">
                                    id_chave
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="38" y="259" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" font-weight="bold" text-decoration="underline">
                        id_chave
                    </text>
                </switch>
            </g>
        </g>
        <g/>
        <g>
            <rect x="0" y="270" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 0 270 M 30 270 M 30 300 M 0 300" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="30" y="270" width="130" height="30" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 30 270 M 160 270 M 160 300 M 30 300" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g/>
        <g>
            <rect x="0" y="300" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 0 300 M 30 300 M 30 330 M 0 330" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="30" y="300" width="130" height="30" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 30 300 M 160 300 M 160 330 M 30 330" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g/>
        <g>
            <rect x="0" y="330" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 0 330 M 30 330 M 30 360 M 0 360" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="30" y="330" width="130" height="30" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 30 330 M 160 330 M 160 360 M 30 360" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 0 440 L 0 410 L 160 410 L 160 440" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 0 440 L 0 570 L 160 570 L 160 440" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 0 440 L 160 440" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 30 440 L 30 470 L 30 500 L 30 530 L 30 560" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 425px; margin-left: 80px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: nowrap; ">
                                    comprovativos
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="80" y="429" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle" font-weight="bold">
                        comprovativos
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 0 440 M 160 440 M 160 470 L 0 470" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="0" y="440" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 0 440 M 30 440 M 30 470 M 0 470" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 28px; height: 1px; padding-top: 455px; margin-left: 1px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; max-height: 26px; overflow: hidden; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; ">
                                    PK
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="15" y="459" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle" font-weight="bold">
                        PK
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="30" y="440" width="130" height="30" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 30 440 M 160 440 M 160 470 M 30 470" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 122px; height: 1px; padding-top: 455px; margin-left: 38px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; max-height: 26px; overflow: hidden; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; text-decoration: underline; white-space: normal; word-wrap: normal; ">
                                    id_chave
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="38" y="459" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" font-weight="bold" text-decoration="underline">
                        id_chave
                    </text>
                </switch>
            </g>
        </g>
        <g/>
        <g>
            <rect x="0" y="470" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 0 470 M 30 470 M 30 500 M 0 500" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="30" y="470" width="130" height="30" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 30 470 M 160 470 M 160 500 M 30 500" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g/>
        <g>
            <rect x="0" y="500" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 0 500 M 30 500 M 30 530 M 0 530" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="30" y="500" width="130" height="30" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 30 500 M 160 500 M 160 530 M 30 530" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g/>
        <g>
            <rect x="0" y="530" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 0 530 M 30 530 M 30 560 M 0 560" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="30" y="530" width="130" height="30" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 30 530 M 160 530 M 160 560 M 30 560" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 160 55 L 180 55 Q 190 55 196.82 62.31 L 323.18 197.69 Q 330 205 340 205 L 360 205" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 352 209 L 352 201 M 360 201 L 352 205 L 360 209" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 160 255 L 180 255 Q 190 255 199.42 251.64 L 320.58 208.36 Q 330 205 340 205 L 360 205" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 352 209 L 352 201 M 360 201 L 352 205 L 360 209" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 160 450 L 180 450 Q 190 450 194.96 441.32 L 325.04 213.68 Q 330 205 340 205 L 360 205" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 352 209 L 352 201 M 360 201 L 352 205 L 360 209" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 645 70 L 568.17 124.23 Q 560 130 556.44 139.34 L 522.27 229.05" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 520.4 233.96 L 519.62 226.17 L 522.27 229.05 L 526.16 228.66 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 120px; margin-left: 581px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    99/100
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="581" y="123" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        99/100
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 520 235 L 611.24 184.82 Q 620 180 622.22 170.25 L 643.59 76.21" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 644.75 71.09 L 646.61 78.69 L 643.59 76.21 L 639.79 77.14 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 192px; margin-left: 593px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    0
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="593" y="196" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        0
                    </text>
                </switch>
            </g>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>