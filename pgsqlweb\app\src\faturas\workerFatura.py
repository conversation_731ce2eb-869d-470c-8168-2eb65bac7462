
from src.services.supabase_client import connect
import io
import zipfile
from datetime import datetime


supabase = connect()
tabela = 'faturas'
bucket = 'documentos/faturas'

def process_uploaded_file(file, entidade, save=False):
    if entidade == "lidl":
        from src.faturas.extractorLidl import FaturaLidlExtractor
        extract_result = FaturaLidlExtractor(pdf=file).run_extraction()
        if save:
            cabec_dict = extract_result.cabecalho.model_dump(exclude_unset=True, exclude_none=True)
            for key, value in cabec_dict.items():
                if isinstance(value, datetime):
                    cabec_dict[key] = value.isoformat()
            insert_cabec = supabase.table('faturas_cabec').insert(cabec_dict).execute()
            id_chave_doc = insert_cabec.data[0]['id_chave']
            for linha in extract_result.linhas:
                linha_dict = linha.model_dump(exclude_unset=True, exclude_none=True)
                linha_dict['id_chave_doc'] = id_chave_doc
                for key, value in linha_dict.items():
                    if isinstance(value, datetime):
                        linha_dict[key] = value.isoformat()
                insert_linha = supabase.table('faturas_linhas').insert(linha_dict).execute()
            return insert_cabec
        else:
            return extract_result # type: ignore
        

    elif entidade == "microsoft":
        raise NotImplementedError("utilizador não implementado")

    return extract_result # type: ignore




def create_zip(files):
    zip_buffer = io.BytesIO()
    with zipfile.ZipFile(zip_buffer, "w") as zf:
        for nome_arquivo, conteudo in files:
            zf.writestr(nome_arquivo, conteudo)
    zip_buffer.seek(0)
    return zip_buffer.getvalue()